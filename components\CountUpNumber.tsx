// components/CountUpNumber.tsx

import React, { useEffect } from 'react';
import { TextInput } from 'react-native';
import Animated, {
    useAnimatedProps,
    useSharedValue,
    withTiming,
} from 'react-native-reanimated';

// This is a necessary step to make TextInput animatable
const AnimatedTextInput = Animated.createAnimatedComponent(TextInput);

interface CountUpNumberProps {
  endValue: number;
  duration?: number;
  style?: any;
}

const CountUpNumber: React.FC<CountUpNumberProps> = ({ endValue, duration = 2000, style }) => {
  const progress = useSharedValue(0);

  useEffect(() => {
    progress.value = withTiming(endValue, { duration });
  }, [endValue, duration]);

  const animatedProps = useAnimatedProps(() => {
    return {
      text: `+${Math.floor(progress.value)}`,
    } as any; // Using 'any' as 'text' is not a standard animatable prop
  });

  return (
    <AnimatedTextInput
      underlineColorAndroid="transparent"
      editable={false}
      value={`+${Math.floor(progress.value)}`}
      style={style}
      animatedProps={animatedProps}
    />
  );
};

export default React.memo(CountUpNumber);