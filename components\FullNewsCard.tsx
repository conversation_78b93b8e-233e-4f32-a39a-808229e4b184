// components/FullNewsCard.tsx

import { Image } from 'expo-image';
import React from 'react';
import {
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface FullNewsCardProps {
  id: string;
  title: string;
  description: string;
  date: string;
  image: any;
  onPress: () => void;
}

const { width } = Dimensions.get('window');

const FullNewsCard: React.FC<FullNewsCardProps> = ({
  title,
  description,
  date,
  image,
  onPress,
}) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.8}>
      <Image source={image} style={styles.image} contentFit="cover" />
      <View style={styles.textContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {title}
        </Text>
        <Text style={styles.date}>{date}</Text>
        <Text style={styles.description} numberOfLines={3}>
          {description}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden',
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  image: {
    width: '100%',
    height: 150,
  },
  textContainer: {
    padding: 12,
    writingDirection: 'rtl',
  },
  title: {
    fontSize: 16,
    fontFamily: 'Rubik-Bold',
    textAlign: 'right',
    color: '#333',
  },
  date: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#888',
    textAlign: 'right',
    marginVertical: 4,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Rubik-Regular',
    color: '#555',
    textAlign: 'right',
    lineHeight: 20,
  },
});

export default FullNewsCard;