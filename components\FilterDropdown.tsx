// components/FilterDropdown.tsx

import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    FlatList,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface FilterDropdownProps {
  options: { label: string; value: string | number }[];
  selectedValue: string | number;
  onSelect: (value: string | number) => void;
  placeholder: string;
}

const FilterDropdown: React.FC<FilterDropdownProps> = ({
  options,
  selectedValue,
  onSelect,
  placeholder,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const selectedLabel = options.find(opt => opt.value === selectedValue)?.label || placeholder;

  return (
    <>
      <TouchableOpacity style={styles.dropdownButton} onPress={() => setModalVisible(true)}>
        <Text style={styles.dropdownButtonText}>{selectedLabel}</Text>
        <Ionicons name="chevron-down-outline" size={20} color="#585858" />
      </TouchableOpacity>

      <Modal
        transparent={true}
        visible={modalVisible}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity style={styles.modalOverlay} onPress={() => setModalVisible(false)}>
          <View style={styles.modalContent}>
            <FlatList
              data={options}
              keyExtractor={(item) => item.value.toString()}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.optionItem}
                  onPress={() => {
                    onSelect(item.value);
                    setModalVisible(false);
                  }}
                >
                  <Text style={styles.optionText}>{item.label}</Text>
                </TouchableOpacity>
              )}
            />
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  dropdownButton: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    backgroundColor: '#FAF6F2',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    minWidth: 120,
    justifyContent: 'space-between',
  },
  dropdownButtonText: {
    fontFamily: 'Rubik-Medium',
    fontSize: 14,
    color: '#F35A30',
    marginRight: 8,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.62)',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 17,
    width: '60%',
    maxHeight: '50%',

  },
  optionItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  optionText: {
    fontFamily: 'Rubik-Regular',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default FilterDropdown;