// components/HomeNews.tsx

import { useRouter } from 'expo-router';
import React from 'react';
import {
  Dimensions,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import HomeNewsCard from './HomeNewsCard';

const { width } = Dimensions.get('window');

// Placeholder data - added a 'type' property
const newsData = [
  {
    id: '1',
    title: 'تأجيل القرعة العلنية لطرح 30 وحدة بمجمع الورش',
    date: '04-07-2025',
    type: 'خبر',
    image: require('@/assets/images/event1.jpg'),
  },
  {
    id: '2',
    title: 'بيع 13 محل تجاري بالحي الثالث عشر في مزاد علني',
    date: '29-11-2023',
    type: 'خبر',
    image: require('@/assets/images/event2.jpg'),
  },
  {
    id: '3',
    title: 'إطلاق المرحلة الجديدة من مشروع الإسكان الشبابي',
    date: '01-07-2025',
    type: 'خبر',
    image: require('@/assets/images/event3.jpg'),
  },
  {
    id: '4',
    title: 'تطوير شبكة الطرق والمواصلات لتسهيل الحركة',
    date: '25-06-2025',
    type: 'خبر',
    image: require('@/assets/images/Home/homepic4.png'),
  },
];

const HomeNews = () => {
  const router = useRouter();

  const handleNewsPress = (newsId: string) => {
    console.log('News item pressed:', newsId);
    router.push('/(drawer)/(tabs)/news');
  };

  const goToAllNews = () => {
    router.push('/(drawer)/(tabs)/news');
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>أخبار المدينة ✨</Text>

      <FlatList
        data={newsData}
        horizontal
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <HomeNewsCard
            id={item.id}
            title={item.title}
            date={item.date}
            image={item.image}
            type={item.type} // Pass the new 'type' prop
            onPress={() => handleNewsPress(item.id)}
          />
        )}
        contentContainerStyle={styles.carouselContainer}
        inverted // Flips the list to start from the right in RTL
      />

      <TouchableOpacity
        onPress={goToAllNews}
        style={styles.button}
      >
        <Text style={styles.buttonText}>عرض كل الأخبار</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    paddingVertical: '4%',
    backgroundColor: '#068E77CC',
    alignItems: 'center',
  },
  title: {
    width: '100%',
    textAlign: 'center',
    writingDirection: 'rtl',
    fontSize: width * 0.05,
    color: '#FFFFFF',
    fontFamily: 'Rubik-ExtraBold',
    marginBottom: 16,
  },
  carouselContainer: {
    paddingHorizontal: 8,
    paddingBottom: 16,
  },
  button: {
    width: '60%',
    marginTop: 24,
    marginBottom: '2%',
    borderRadius: 29,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F35A30',
  },
  buttonText: {
    color: 'white',
    fontSize: width * 0.04,
    fontFamily: 'Rubik-Bold',
    textAlign: 'center',
  },
});

export default HomeNews;