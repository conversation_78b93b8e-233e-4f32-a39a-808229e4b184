// app/(drawer)/about.tsx

import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React, { useState } from 'react';
import {
  Dimensions,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
// Import carousel and pagination components
import CountUpNumber from '@/components/CountUpNumber';
import { useSharedValue } from 'react-native-reanimated';
import Carousel, { Pagination } from 'react-native-reanimated-carousel';

const { width } = Dimensions.get('window');

// --- Component for the Image Slider with Pagination ---
const AboutSlider = () => {
  const images = [
    require('@/assets/images/Home/homepic1.png'),
    require('@/assets/images/Home/homepic2.png'),
    require('@/assets/images/Home/homepic3.png'),
    require('@/assets/images/Home/homepic4.png'),
  ];
  
  // Shared value to track carousel progress for pagination
  const progressValue = useSharedValue<number>(0);
  
  return (
    <View style={styles.sliderComponentContainer}>
      <Carousel
        loop
        width={width * 0.9 - 32} // Card width based on parent padding
        height={300}
        autoPlay={true}
        data={images}
        scrollAnimationDuration={1000}
        onProgressChange={(_, absoluteProgress) => (progressValue.value = absoluteProgress)}
        renderItem={({ item }) => (
          <View style={styles.sliderContainer}>
            <Image source={item} style={styles.sliderImage} contentFit="cover" />
          </View>
        )}
      />
      <Pagination.Basic
        progress={progressValue}
        data={images}
        dotStyle={styles.paginationDot}
        activeDotStyle={styles.paginationActiveDot}
        containerStyle={styles.paginationContainer}
      />
    </View>
  );
};

// --- Component for the Map Section ---
const ZayedMap = () => {
  const [modalVisible, setModalVisible] = useState(false);
  return (
    <>
      <View style={styles.mapSectionContainer}>
        <Text style={styles.sectionTitle}>📌 المخطط العام</Text>
        <Text style={styles.sectionSubtitle}>
          تبلغ المساحة الإجمالية للمدينة 21.13 ألف فدان منها 20.94 ألف كتلة عمرانية (مناطق سكنية – خدمية – صناعية – سياحية و ترفيهية)
        </Text>
        <TouchableOpacity onPress={() => setModalVisible(true)} activeOpacity={0.8}>
          <Image source={require('@/assets/images/map.png')} style={styles.mapImage} />
        </TouchableOpacity>
      </View>

      <Modal visible={modalVisible} transparent={true} onRequestClose={() => setModalVisible(false)} animationType="fade">
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={() => setModalVisible(false)}>
            <Ionicons name="close-circle" size={40} color="white" />
          </TouchableOpacity>
          <Image source={require('@/assets/images/map.png')} style={styles.fullscreenImage} contentFit="contain" />
        </View>
      </Modal>
    </>
  );
};

// --- Component for the Statistics Section ---
const ZayedNumbers = () => {
  const data = [
    { image: require('@/assets/images/home.svg'), end: 98, bigText: 'ألف وحدة سكنية', smallText: 'لمختلف الفئات' },
    { image: require('@/assets/images/teamwork.svg'), end: 408, bigText: 'ألف نسمة', smallText: 'عدد السكان الحالي' },
    { image: require('@/assets/images/school.svg'), end: 27, bigText: 'مدرسة تعليمية', smallText: 'أساسي، تجريبي، أزهري، ثانوي، فندقي' },
  ];

  return (
    <View style={styles.numbersSectionContainer}>
      <Text style={styles.sectionTitle}>بعض الإحصائيات</Text>
      <View style={styles.numbersGrid}>
        {data.map((item, index) => (
          <View key={index} style={styles.numberCard}>
            <Image style={styles.numberIcon} source={item.image} />
            <CountUpNumber endValue={item.end} style={styles.numberText} />
            <Text style={styles.numberBigText}>{item.bigText}</Text>
            <Text style={styles.numberSmallText}>{item.smallText}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

// --- Main About Screen Component ---
export default function AboutScreen() {
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.topSection}>
          <AboutSlider />
          <View style={styles.aboutTextContainer}>
            <Text style={styles.aboutTitle}>📌 عن مدينة الشيخ زايد</Text>
            <Text style={styles.aboutDescription}>
              مدينة السعادة, كما يطلق عليها سكانها وروادها من كل مكان, فمنذ إنشاء المدينة أخذت طابعاً سياحياً وترفيهياً وتجارياً.
              {'\n\n'}
              تم إنشاء مدينة الشيخ زايد بالقرار الجمهوري رقم 325 لسنة 1995. وتقع على ربوة مرتفعة عن سطح البحر وترتبط بشبكة من الطرق الهامة، وتعتبر مدينة خضراء صديقة للبيئة.
            </Text>
          </View>
        </View>
        <ZayedMap />
        <ZayedNumbers />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: '#EBDECE',paddingTop: 120, },
  scrollContainer: {
    alignItems: 'center',
    paddingBottom: 40,
  },
  topSection: {
    width: '90%',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 32,
    padding: 16,
    alignItems: 'center', // Center slider and its pagination
  },
  sliderComponentContainer: {
    width: '100%',
    alignItems: 'center',
  },
  sliderContainer: {
    borderRadius: 24,
    overflow: 'hidden',
    width: '100%',
  },
  sliderImage: {
    width: '100%',
    height: '100%',
  },
  paginationContainer: {
    marginTop: 15,
    marginBottom: -5, // Pull it closer to the slider
  },
  paginationDot: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    width: 8,
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
  paginationActiveDot: {
    backgroundColor: '#F35A30',
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  aboutTextContainer: {
    marginTop: 20,
    width: '100%',
  },
  aboutTitle: {
    fontSize: width * 0.055,
    fontFamily: 'Rubik-Bold',
    textAlign: 'center',
    marginBottom: 12,
    color: '#333',
  },
  aboutDescription: {
    fontSize: 17,
    fontFamily: 'Rubik-Regular',
    textAlign: 'justify',
    lineHeight: 24,
    color: '#555',
  },
  mapSectionContainer: {
    width: '100%',
    marginTop: '6%',
    paddingVertical: '5%',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    alignItems: 'center',
  },
  sectionTitle: {
    fontSize: width * 0.06,
    fontFamily: 'Rubik-Bold',
    color: '#000',
    marginBottom: 8,
    textAlign: 'center', // This ensures titles are centered
    
  },
  sectionSubtitle: {
    fontSize: width * 0.038,
    textAlign: 'center',
    fontFamily: 'Rubik-Medium',
    color: '#333',
    marginBottom: 16,
    paddingHorizontal: '5%',
  },
  mapImage: {
    width: width * 0.8,
    height: width * 0.5,
    borderRadius: 16,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.85)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenImage: {
    width: '95%',
    height: '80%',
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: 20,
  },
  numbersSectionContainer: {
    width: '100%',
    marginTop: '6%',
    paddingHorizontal: '5%',
    alignItems: 'center',
  },
  numbersGrid: {
    marginTop: 16,
    width: '100%',
    alignItems: 'center', // For single column layout on smaller screens
  },
  numberCard: {
    width: '100%', // Take full width on small screens
    maxWidth: 350, // Max width for larger screens
    aspectRatio: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    marginBottom: 16,
  },
  numberIcon: {
    width: '30%',
    height: '30%',
    marginBottom: 8,
    contentFit: 'contain',
  },
  numberText: {
    color: '#F35A30',
    fontFamily: 'Rubik-Bold',
    fontSize: width * 0.12,
  },
  numberBigText: {
    fontSize: width * 0.05,
    fontFamily: 'Rubik-Bold',
    color: '#333',
    textAlign: 'center',
  },
  numberSmallText: {
    fontSize: width * 0.03,
    fontFamily: 'Rubik-Regular',
    color: '#666',
    textAlign: 'center',
    marginTop: 4,
  },
});