// components/NewsCarouselCard.tsx

import { Image } from 'expo-image';
import React from 'react';
import {
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface NewsCardProps {
  item: {
    id: string;
    title: string;
    date: string;
    image: any;
    category: string;
  };
  onPress: () => void;
}

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.85;

const NewsCarouselCard: React.FC<NewsCardProps> = ({ item, onPress }) => {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress} activeOpacity={0.9}>
      <View style={styles.imageContainer}>
        <Image source={item.image} style={styles.image} contentFit="contain" />
      </View>
      <View style={styles.textContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {item.title}
        </Text>
        <View style={styles.footer}>
          <Text style={styles.date}>{new Date(item.date).toLocaleDateString('ar-EG')}</Text>
          <View style={styles.tag}>
            <Text style={styles.tagText}>{item.category}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: CARD_WIDTH,
    backgroundColor: 'white',
    borderRadius: 24,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    alignSelf: 'center',
  },
  imageContainer: {
    width: '100%',
    height: 350,
    backgroundColor: '#f0f0f0',
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    padding: 20,
  },
  title: {
    fontFamily: 'Rubik-Bold',
    fontSize: 18,
    color: '#F35A30',
    textAlign: 'right',
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  date: {
    fontFamily: 'Rubik-Regular',
    fontSize: 14,
    color: '#666',
  },
  tag: {
    backgroundColor: '#3b5998',
    paddingVertical: 4,
    paddingHorizontal: 16,
    borderRadius: 12,
  },
  tagText: {
    fontFamily: 'Rubik-Medium',
    fontSize: 12,
    color: 'white',
  },
});

export default NewsCarouselCard;