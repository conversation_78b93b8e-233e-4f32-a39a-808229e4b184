// components/NewsCarouselCard.tsx

import { Image } from 'expo-image';
import React from 'react';
import {
    Dimensions,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface NewsCardProps {
  item: {
    id: string;
    title: string;
    date: string;
    description: string;
    image: any;
    category: string;
  };
  onPress: () => void;
}

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.85;

const NewsCarouselCard: React.FC<NewsCardProps> = ({ item, onPress }) => {
  return (
    <View style={styles.card}>
      <View style={styles.imageContainer}>
        <Image source={item.image} style={styles.image} contentFit="cover" />
      </View>

      <View style={styles.textContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {item.title}
        </Text>

        <Text style={styles.description} numberOfLines={4}>
          {item.description}
        </Text>

        <View style={styles.footer}>
          <Text style={styles.date}>{new Date(item.date).toLocaleDateString('ar-EG')}</Text>
          <TouchableOpacity style={styles.newsButton} onPress={onPress} activeOpacity={0.8}>
            <Text style={styles.newsButtonText}>خبر</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    width: CARD_WIDTH,
    backgroundColor: 'white',
    borderRadius: 24,
    borderWidth: 3,
    borderColor: '#4A90E2',
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    alignSelf: 'center',
    marginHorizontal: 10,
  },
  imageContainer: {
    width: '100%',
    height: 280,
    backgroundColor: '#f0f0f0',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  textContainer: {
    padding: 16,
  },
  title: {
    fontFamily: 'Rubik-Bold',
    fontSize: 16,
    color: '#F35A30',
    textAlign: 'right',
    marginBottom: 8,
    lineHeight: 24,
  },
  description: {
    fontFamily: 'Rubik-Regular',
    fontSize: 14,
    color: '#333',
    textAlign: 'right',
    lineHeight: 20,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  date: {
    fontFamily: 'Rubik-Regular',
    fontSize: 12,
    color: '#666',
  },
  newsButton: {
    backgroundColor: '#4A90E2',
    paddingVertical: 6,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  newsButtonText: {
    fontFamily: 'Rubik-Bold',
    fontSize: 14,
    color: 'white',
    textAlign: 'center',
  },
});

export default NewsCarouselCard;