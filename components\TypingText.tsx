// components/TypingText.tsx
import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withRepeat, withTiming } from 'react-native-reanimated';

export const TypingText = () => {
  const [displayedText, setDisplayedText] = useState('');
  const fullText = 'مدينة السعادة';
  const cursorOpacity = useSharedValue(1);

  useEffect(() => {
    let currentIndex = 0;
    const typingInterval = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setDisplayedText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
      }
    }, 80);

    cursorOpacity.value = withRepeat(withTiming(0, { duration: 500 }), -1, true);

    return () => clearInterval(typingInterval);
  }, []);
  
  const cursorAnimatedStyle = useAnimatedStyle(() => ({ opacity: cursorOpacity.value }));

  return (
    <View style={styles.typedTextWrapper}>
      <Text style={styles.animatedText}>
        {displayedText}
        <Animated.Text style={[styles.cursor, cursorAnimatedStyle]}>|</Animated.Text>
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  typedTextWrapper: { alignItems: 'center', marginBottom: 12 },
  animatedText: {fontFamily: 'Rubik-Bold', fontSize: 32, color: '#F35A30', textAlign: 'center', writingDirection: 'rtl' },
  cursor: { fontFamily: 'Rubik-Bold', fontSize: 32, fontWeight: 'bold', color: '#F35A30' },
});