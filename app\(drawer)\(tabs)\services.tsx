// app/(drawer)/(tabs)/services.tsx

import FieldCard from '@/components/FieldCard';
import { Image } from 'expo-image';
import { useLocalSearchParams } from 'expo-router';
import React, { useEffect, useMemo, useState } from 'react';
import {
  Dimensions,
  FlatList,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width } = Dimensions.get('window');

const placeholderServices = [
    { id: '1', name: 'خدمات تعليمية', icon: require('@/assets/images/serv/Group57.svg')},
    { id: '2', name: 'خدمات صحية', icon: require('@/assets/images/serv/Group7.svg')},
    { id: '3', name: 'خدمات حكومية', icon: require('@/assets/images/serv/Group8.svg')},
    { id: '4', name: 'فنادق و عقارات', icon: require('@/assets/images/serv/Group65.svg')},
    { id: '5', name: 'بنوك و اتصالات', icon: require('@/assets/images/serv/Group58.svg') },
    { id: '6', name: 'مراكز التسوق', icon: require('@/assets/images/serv/Group10.svg') },
    { id: '7', name: 'أماكن الترفيه', icon: require('@/assets/images/serv/Group.svg') },
    { id: '8', name: 'أماكن العبادة', icon: require('@/assets/images/serv/Group72.svg') },
    { id: '9', name: 'أماكن الرياضة', icon: require('@/assets/images/serv/Group67.svg') },
];

const placeholderSections = [
  { id: 's1', serviceId: '1', name: 'مدارس' },
  { id: 's2', serviceId: '1', name: 'جامعات' },
  { id: 's3', serviceId: '1', name: 'حضانات' },
  { id: 's4', serviceId: '2', name: 'مستشفيات' },
  { id: 's5', serviceId: '2', name: 'صيدليات' },
  { id: 's6', serviceId: '3', name: 'مكاتب بريد' },
  { id: 's7', serviceId: '3', name: 'سجلات مدنية' },
];

const placeholderFields = [
  { id: 'f1', sectionId: 's1', name: 'مدرسة الأفق الجديدة', description: 'تعليم أساسي وثانوي', phone_number: '0238500001', location: 'الحي الأول', lat: 30.05, long: 30.95, image: require('@/assets/images/event1.jpg') },
  { id: 'f2', sectionId: 's2', name: 'جامعة النيل', description: 'تعليم عالي وبحث علمي', phone_number: '16058', location: 'محور 26 يوليو', lat: 30.02, long: 31.01, image: require('@/assets/images/event2.jpg') },
  { id: 'f3', sectionId: 's4', name: 'مستشفى دار الفؤاد', description: 'خدمات طبية متكاملة', phone_number: '194488', location: 'محور 26 يوليو', lat: 30.02, long: 31.02, image: require('@/assets/images/event3.jpg')},
];

export default function ServicesScreen() {
  const params = useLocalSearchParams<{ serviceId?: string }>();

  const [selectedService, setSelectedService] = useState<string | null>(
    params.serviceId || (placeholderServices.length > 0 ? placeholderServices[0].id : null)
  );
  const [selectedSubService, setSelectedSubService] = useState<string | null>(null);

  useEffect(() => {
    if (params.serviceId && params.serviceId !== selectedService) {
      setSelectedService(params.serviceId);
    }
  }, [params.serviceId]);

  const filteredSections = useMemo(() => {
    if (!selectedService) return [];
    return placeholderSections.filter(s => s.serviceId === selectedService);
  }, [selectedService]);

  useEffect(() => {
    if (filteredSections.length > 0) {
      const currentSubServiceStillValid = filteredSections.some(s => s.id === selectedSubService);
      if (!currentSubServiceStillValid) {
        setSelectedSubService(filteredSections[0].id);
      }
    } else {
      setSelectedSubService(null);
    }
  }, [filteredSections, selectedSubService]);

  const filteredFields = useMemo(() => {
    if (!selectedSubService) return [];
    return placeholderFields.filter(f => f.sectionId === selectedSubService);
  }, [selectedSubService]);

  const renderServiceItem = ({ item }: { item: typeof placeholderServices[0] }) => {
    const isSelected = item.id === selectedService;
    return (
      <TouchableOpacity
        style={[styles.serviceCard, isSelected && styles.serviceCardSelected]}
        onPress={() => setSelectedService(item.id)}
      >
        <Image source={item.icon} style={styles.serviceIcon} contentFit="contain" />
        <Text style={[styles.serviceText, isSelected && styles.serviceTextSelected]}>
          {item.name}
        </Text>
      </TouchableOpacity>
    );
  };
  
  const renderSectionItem = ({ item }: { item: typeof filteredSections[0] }) => {
    const isSelected = item.id === selectedSubService;
    return (
      <TouchableOpacity
        style={[styles.sectionButton, isSelected && styles.sectionButtonSelected]}
        onPress={() => setSelectedSubService(item.id)}
      >
        <Text style={[styles.sectionText, isSelected && styles.sectionTextSelected]}>
          {item.name}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.headerContainer}>
          <Text style={styles.title}>☎️ دليل الخدمات</Text>
          <Text style={styles.subtitle}>
            تواصل بشكل أسرع مع دليل الخدمات الحكومية و الأنشطة التجارية و المتاجر و الخدمات العامة
          </Text>
        </View>

        <FlatList
          horizontal
          inverted
          showsHorizontalScrollIndicator={false}
          data={placeholderServices}
          renderItem={renderServiceItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.carouselContainer}
        />

        <View style={styles.sectionCarouselWrapper}>
          <FlatList
            horizontal
            inverted
            showsHorizontalScrollIndicator={false}
            data={filteredSections}
            renderItem={renderSectionItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.carouselContainer}
          />
        </View>
        
        <View style={styles.fieldsContainer}>
          {filteredFields.length > 0 ? (
            filteredFields.map(field => <FieldCard key={field.id} field={field} />)
          ) : (
            <View style={styles.noResultsContainer}>
               <Image source={require('@/assets/images/serv/Group.svg')} style={styles.noResultsImage} />
              <Text style={styles.noResultsText}>لا يوجد محتوى فى الوقت الحالى</Text>
              <Text style={styles.noResultsSubtitle}>يمكنك زيارة الصفحة فى وقت لاحق</Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: '#EBDECE' },
  scrollContainer: { paddingTop: 100, paddingBottom: 40, alignItems: 'center' },
  headerContainer: { alignItems: 'center', marginBottom: 24, paddingHorizontal: 16 },
  title: { fontSize: width * 0.06, fontFamily: 'Rubik-Bold', color: '#068E77' },
  subtitle: { fontSize: width * 0.04, fontFamily: 'Rubik-Medium', color: '#333', textAlign: 'center', marginTop: 8, lineHeight: 24 },
  carouselContainer: { paddingHorizontal: 16, gap: 12 },
  serviceCard: {
    width: 100,
    height: 100,
    backgroundColor: 'white',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  serviceCardSelected: { backgroundColor: '#068E77' },
  serviceIcon: { width: '50%', height: '45%', marginBottom: 4 },
  serviceText: { fontSize: 11, fontFamily: 'Rubik-Bold', color: '#068E77', textAlign: 'center' },
  serviceTextSelected: { color: 'white' },
  sectionCarouselWrapper: {
    width: '100%',
    height: 55,
    backgroundColor: 'white',
    marginTop: 20,
    justifyContent: 'center',
  },
  sectionButton: { paddingHorizontal: 20, paddingVertical: 8, borderRadius: 30 },
  sectionButtonSelected: { backgroundColor: '#F35A30' },
  sectionText: { color: '#068E77', fontFamily: 'Rubik-Bold', fontSize: 15 },
  sectionTextSelected: { color: 'white' },
  fieldsContainer: { width: '90%', marginTop: 24 },
  noResultsContainer: { alignItems: 'center', marginTop: 40 },
  noResultsImage: { width: 100, height: 100, opacity: 0.5, marginBottom: 16 },
  noResultsText: { fontSize: 18, fontFamily: 'Rubik-Bold', color: '#068E77' },
  noResultsSubtitle: { fontSize: 14, fontFamily: 'Rubik-Regular', color: '#555', marginTop: 4 },
});