# Modern Drawer Navigation Options for React Native

This document contains the best drawer navigation libraries and implementations for integrating with your HomeHeader component.

## 1. React Navigation Drawer (Recommended)

**Description:** The most popular and well-maintained drawer solution with excellent performance.

**Features:**
- Native performance with gesture support
- Highly customizable with smooth animations
- Built-in accessibility features
- Works seamlessly with Expo Router
- Active community support

**Installation:**
```bash
npm install @react-navigation/drawer react-native-gesture-handler react-native-reanimated
```

**Implementation:**
```tsx
// components/CustomDrawer.tsx
import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, SafeAreaView } from 'react-native';
import { DrawerContentScrollView, DrawerItem } from '@react-navigation/drawer';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { Image } from 'expo-image';
import { router } from 'expo-router';

export function CustomDrawerContent(props: any) {
  const menuItems = [
    { name: 'الصفحة الرئيسية', icon: 'home-outline', route: '/' },
    { name: 'الأخبار', icon: 'newspaper-outline', route: '/news' },
    { name: 'الخدمات', icon: 'grid-outline', route: '/services' },
    { name: 'الفعاليات', icon: 'calendar-outline', route: '/events' },
    { name: 'حول المدينة', icon: 'information-circle-outline', route: '/about' },
    { name: 'اتصل بنا', icon: 'call-outline', route: '/contact' },
    { name: 'الإعدادات', icon: 'settings-outline', route: '/settings' },
  ];

  return (
    <BlurView
      intensity={15}
      tint="light"
      style={styles.container}
      experimentalBlurMethod='dimezisBlurView'
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.colorOverlay} />
        
        {/* Header Section */}
        <View style={styles.drawerHeader}>
          <Image
            source={require('@/assets/images/Logo.svg')}
            style={styles.logo}
            contentFit="contain"
          />
          <Text style={styles.appName}>مدينة الشيخ زايد</Text>
          <Text style={styles.subtitle}>مدينة السعادة</Text>
        </View>

        {/* Menu Items */}
        <DrawerContentScrollView 
          {...props} 
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={() => {
                router.push(item.route);
                props.navigation.closeDrawer();
              }}
              activeOpacity={0.7}
            >
              <View style={styles.menuItemContent}>
                <Ionicons 
                  name={item.icon} 
                  size={24} 
                  color="#068E77" 
                  style={styles.menuIcon}
                />
                <Text style={styles.menuText}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </DrawerContentScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.version}>الإصدار 1.0.0</Text>
        </View>
      </SafeAreaView>
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  colorOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  drawerHeader: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(6, 142, 119, 0.2)',
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 10,
  },
  appName: {
    fontSize: 18,
    fontFamily: 'Rubik-Bold',
    color: '#068E77',
    textAlign: 'center',
    writingDirection: 'rtl',
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Rubik-Medium',
    color: '#F35A30',
    textAlign: 'center',
    writingDirection: 'rtl',
    marginTop: 4,
  },
  scrollContent: {
    paddingTop: 20,
  },
  menuItem: {
    marginHorizontal: 15,
    marginVertical: 3,
    borderRadius: 12,
    overflow: 'hidden',
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
  },
  menuIcon: {
    marginRight: 15,
  },
  menuText: {
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    color: '#333',
    textAlign: 'right',
    writingDirection: 'rtl',
    flex: 1,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(6, 142, 119, 0.2)',
  },
  version: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#666',
  },
});
```

**Navigation Setup:**
```tsx
// app/(tabs)/_layout.tsx - Updated with Drawer
import { createDrawerNavigator } from '@react-navigation/drawer';
import { CustomDrawerContent } from '@/components/CustomDrawer';
import ModernTabBar from '@/components/ModernTabBar';

const Drawer = createDrawerNavigator();

export default function TabLayout() {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerType: 'slide',
        overlayColor: 'rgba(0, 0, 0, 0.3)',
        drawerStyle: {
          width: '75%',
          backgroundColor: 'transparent',
        },
        sceneContainerStyle: {
          backgroundColor: '#EBDECE',
        },
      }}
    >
      <Drawer.Screen name="Main" component={YourTabNavigator} />
    </Drawer.Navigator>
  );
}
```

## 2. React Native Side Menu (Lightweight)

**Description:** Lightweight alternative with excellent performance and customization.

**Features:**
- Minimal dependencies
- Smooth gesture animations
- Easy customization
- Good performance on older devices

**Installation:**
```bash
npm install react-native-side-menu-updated
```

**Implementation:**
```tsx
// components/SideMenuDrawer.tsx
import React, { useState } from 'react';
import SideMenu from 'react-native-side-menu-updated';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

interface SideMenuDrawerProps {
  children: React.ReactNode;
  isOpen: boolean;
  onToggle: () => void;
}

const MenuContent = ({ onMenuItemPress }: { onMenuItemPress: (route: string) => void }) => {
  const menuItems = [
    { name: 'الصفحة الرئيسية', icon: 'home-outline', route: '/' },
    { name: 'الأخبار', icon: 'newspaper-outline', route: '/news' },
    { name: 'الخدمات', icon: 'grid-outline', route: '/services' },
    { name: 'الفعاليات', icon: 'calendar-outline', route: '/events' },
  ];

  return (
    <BlurView intensity={20} tint="light" style={styles.menuContainer}>
      <View style={styles.menuContent}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={() => onMenuItemPress(item.route)}
          >
            <Ionicons name={item.icon} size={24} color="#068E77" />
            <Text style={styles.menuText}>{item.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </BlurView>
  );
};

export default function SideMenuDrawer({ children, isOpen, onToggle }: SideMenuDrawerProps) {
  const handleMenuItemPress = (route: string) => {
    // Navigate to route
    onToggle(); // Close menu
  };

  return (
    <SideMenu
      menu={<MenuContent onMenuItemPress={handleMenuItemPress} />}
      isOpen={isOpen}
      onChange={onToggle}
      menuPosition="right"
      animationStyle="slide"
      animationFunction={(prop, value) => Easing.inOut(Easing.ease)(prop, value)}
      openMenuOffset={250}
      bounceBackOnOverdraw={false}
    >
      {children}
    </SideMenu>
  );
}

const styles = StyleSheet.create({
  menuContainer: {
    flex: 1,
    paddingTop: 60,
  },
  menuContent: {
    flex: 1,
    padding: 20,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    marginVertical: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 12,
  },
  menuText: {
    marginLeft: 15,
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    color: '#333',
    writingDirection: 'rtl',
  },
});
```

## 3. React Native Drawer Layout (Native Performance)

**Description:** Uses native drawer components for maximum performance.

**Installation:**
```bash
npm install react-native-drawer-layout
```

**Implementation:**
```tsx
// components/NativeDrawer.tsx
import React, { useRef } from 'react';
import DrawerLayout from 'react-native-drawer-layout';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

interface NativeDrawerProps {
  children: React.ReactNode;
  onDrawerOpen?: () => void;
}

export default function NativeDrawer({ children, onDrawerOpen }: NativeDrawerProps) {
  const drawerRef = useRef<DrawerLayout>(null);

  const openDrawer = () => {
    drawerRef.current?.openDrawer();
    onDrawerOpen?.();
  };

  const renderDrawerContent = () => (
    <BlurView intensity={15} tint="light" style={styles.drawerContent}>
      <View style={styles.header}>
        <Text style={styles.title}>القائمة الرئيسية</Text>
      </View>
      
      <View style={styles.menuItems}>
        {[
          { name: 'الصفحة الرئيسية', icon: 'home-outline' },
          { name: 'الأخبار', icon: 'newspaper-outline' },
          { name: 'الخدمات', icon: 'grid-outline' },
          { name: 'الفعاليات', icon: 'calendar-outline' },
        ].map((item, index) => (
          <TouchableOpacity key={index} style={styles.menuItem}>
            <Ionicons name={item.icon} size={24} color="#068E77" />
            <Text style={styles.menuText}>{item.name}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </BlurView>
  );

  return (
    <DrawerLayout
      ref={drawerRef}
      drawerWidth={280}
      drawerPosition="right"
      renderNavigationView={renderDrawerContent}
      drawerBackgroundColor="transparent"
    >
      {children}
    </DrawerLayout>
  );
}

const styles = StyleSheet.create({
  drawerContent: {
    flex: 1,
    paddingTop: 60,
  },
  header: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(6, 142, 119, 0.2)',
  },
  title: {
    fontSize: 20,
    fontFamily: 'Rubik-Bold',
    color: '#068E77',
    textAlign: 'center',
    writingDirection: 'rtl',
  },
  menuItems: {
    flex: 1,
    padding: 15,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    marginVertical: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
  },
  menuText: {
    marginLeft: 15,
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    color: '#333',
    writingDirection: 'rtl',
  },
});
```

## 4. Custom Animated Drawer (Lightweight)

**Description:** Custom implementation using React Native Reanimated for maximum control.

**Implementation:**
```tsx
// components/AnimatedDrawer.tsx
import React from 'react';
import { View, StyleSheet, TouchableOpacity, Text, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  runOnJS,
} from 'react-native-reanimated';
import { BlurView } from 'expo-blur';
import { Ionicons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');
const DRAWER_WIDTH = width * 0.75;

interface AnimatedDrawerProps {
  children: React.ReactNode;
  isVisible: boolean;
  onClose: () => void;
}

export default function AnimatedDrawer({ children, isVisible, onClose }: AnimatedDrawerProps) {
  const translateX = useSharedValue(DRAWER_WIDTH);
  const overlayOpacity = useSharedValue(0);

  React.useEffect(() => {
    translateX.value = withTiming(isVisible ? 0 : DRAWER_WIDTH, { duration: 300 });
    overlayOpacity.value = withTiming(isVisible ? 1 : 0, { duration: 300 });
  }, [isVisible]);

  const drawerStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: translateX.value }],
  }));

  const overlayStyle = useAnimatedStyle(() => ({
    opacity: overlayOpacity.value,
  }));

  if (!isVisible && translateX.value === DRAWER_WIDTH) {
    return <>{children}</>;
  }

  return (
    <View style={styles.container}>
      {children}
      
      <Animated.View style={[styles.overlay, overlayStyle]}>
        <TouchableOpacity 
          style={styles.overlayTouch} 
          onPress={onClose}
          activeOpacity={1}
        />
      </Animated.View>

      <Animated.View style={[styles.drawer, drawerStyle]}>
        <BlurView intensity={20} tint="light" style={styles.drawerContent}>
          <View style={styles.drawerHeader}>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Ionicons name="close" size={24} color="#068E77" />
            </TouchableOpacity>
            <Text style={styles.drawerTitle}>القائمة الرئيسية</Text>
          </View>

          <View style={styles.menuList}>
            {[
              { name: 'الصفحة الرئيسية', icon: 'home-outline' },
              { name: 'الأخبار', icon: 'newspaper-outline' },
              { name: 'الخدمات', icon: 'grid-outline' },
              { name: 'الفعاليات', icon: 'calendar-outline' },
            ].map((item, index) => (
              <TouchableOpacity key={index} style={styles.menuItem}>
                <Ionicons name={item.icon} size={24} color="#068E77" />
                <Text style={styles.menuText}>{item.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </BlurView>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
  },
  overlayTouch: {
    flex: 1,
  },
  drawer: {
    position: 'absolute',
    top: 0,
    right: 0,
    bottom: 0,
    width: DRAWER_WIDTH,
    zIndex: 1001,
  },
  drawerContent: {
    flex: 1,
    paddingTop: 60,
  },
  drawerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(6, 142, 119, 0.2)',
  },
  closeButton: {
    padding: 8,
  },
  drawerTitle: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Rubik-Bold',
    color: '#068E77',
    textAlign: 'center',
    writingDirection: 'rtl',
  },
  menuList: {
    flex: 1,
    padding: 15,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    marginVertical: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 12,
  },
  menuText: {
    marginLeft: 15,
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    color: '#333',
    writingDirection: 'rtl',
  },
});
```

## Integration with HomeHeader

**Updated HomeHeader with Drawer Support:**
```tsx
// components/HomeHeader.tsx - Updated
import React from 'react';
import { BlurView } from 'expo-blur';
import { Image } from 'expo-image';
import { StyleSheet, View, TouchableOpacity } from 'react-native';

interface HomeHeaderProps {
  onMenuPress?: () => void;
  onNotificationPress?: () => void;
}

const HomeHeader: React.FC<HomeHeaderProps> = ({ onMenuPress, onNotificationPress }) => {
  return (
    <View style={styles.container}>
      <BlurView
        intensity={9.2}
        tint="systemUltraThinMaterialLight"
        style={styles.blurView}
        experimentalBlurMethod='dimezisBlurView'
        blurReductionFactor={5}
      >
        <View style={styles.colorOverlay} />
        <View style={styles.header}>
          {/* Logo on the left */}
          <View style={styles.logoContainer}>
            <Image
              source={require('@/assets/images/Logo.svg')}
              style={styles.logo}
              contentFit="contain"
            />
          </View>
          
          {/* Right side icons */}
          <View style={styles.rightIconsContainer}>
            <TouchableOpacity 
              style={styles.iconButton}
              onPress={onNotificationPress}
              activeOpacity={0.7}
            >
              <Image
                source={require('@/assets/images/notification.svg')}
                style={styles.rightIcon}
                contentFit="contain"
              />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.iconButton}
              onPress={onMenuPress}
              activeOpacity={0.7}
            >
              <Image
                source={require('@/assets/images/RightMenu.svg')}
                style={styles.rightIcon}
                contentFit="contain"
              />
            </TouchableOpacity>
          </View>
        </View>
      </BlurView>
    </View>
  );
};

export default HomeHeader;
```

## Comparison Table

| Feature | React Navigation | Side Menu | Drawer Layout | Custom Animated |
|---------|------------------|-----------|---------------|-----------------|
| **Performance** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Customization** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Ease of Use** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Dependencies** | 3 packages | 1 package | 1 package | None |
| **Bundle Size** | Medium | Small | Small | Minimal |
| **Gestures** | Native | Good | Native | Custom |
| **RTL Support** | Excellent | Good | Good | Custom |

## Recommendations

1. **React Navigation Drawer** - Best for complex apps with full navigation integration
2. **Custom Animated Drawer** - Best for maximum control and minimal dependencies
3. **React Native Drawer Layout** - Best for native performance
4. **Side Menu** - Best for simple implementations

Choose based on your specific needs for performance, customization, and complexity requirements.