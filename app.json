{"expo": {"name": "zayed", "slug": "zayed", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "zayed", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.omaraglan96.zayed", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": false, "package": "com.omaraglan96.zayed"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "contentFit": "contain", "backgroundColor": "#ffffff"}], "expo-font"], "experiments": {"typedRoutes": true, "reactcompiler": true}, "extra": {"router": {}, "eas": {"projectId": "116853f8-4059-434a-94f9-8cc83e520272"}}}}