// components/HomeServices.tsx

import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import React from 'react';
import {
  Dimensions,
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface ServiceItem {
  id: string;
  name: string;
  icon: any;
  color: string;
}
const { width: screenWidth } = Dimensions.get('window');

const PLACEHOLDER_SERVICES: ServiceItem[] = [
  { id: '1', name: 'خدمات تعليمية', icon: require('@/assets/images/serv/Group57.svg'), color: '#4A90E2' },
  { id: '2', name: 'خدمات صحية', icon: require('@/assets/images/serv/Group7.svg'), color: '#FF6B6B' },
  { id: '3', name: 'خدمات حكومية', icon: require('@/assets/images/serv/Group8.svg'), color: '#4ECDC4' },
  { id: '4', name: 'فنادق و عقارات', icon: require('@/assets/images/serv/Group65.svg'), color: '#45B7D1' },
  { id: '5', name: 'بنوك و اتصالات', icon: require('@/assets/images/serv/Group58.svg'), color: '#96CEB4' },
  { id: '6', name: 'مراكز التسوق', icon: require('@/assets/images/serv/Group10.svg'), color: '#FFEAA7' },
  { id: '7', name: 'أماكن الترفيه', icon: require('@/assets/images/serv/Group.svg'), color: '#DDA0DD' },
  { id: '8', name: 'أماكن العبادة', icon: require('@/assets/images/serv/Group72.svg'), color: '#98D8C8' },
  { id: '9', name: 'أماكن الرياضة', icon: require('@/assets/images/serv/Group67.svg'), color: '#F7DC6F' },
];

const HomeServices = () => {
  const router = useRouter();

  const goToThisService = (serviceId: string) => {
    router.push({
      pathname: '/(drawer)/(tabs)/services',
      params: { serviceId },
    });
  };

  const renderServiceItem = ({ item }: { item: ServiceItem }) => (
    <View style={styles.serviceWrapper}>
      <TouchableOpacity
        style={[styles.serviceItemContainer, { borderColor: item.color + '20' }]}
        onPress={() => goToThisService(item.id)}
      >
        <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
          <Image
            source={item.icon}
            style={styles.iconImage}
            contentFit="contain"
          />
        </View>
      </TouchableOpacity>
      <Text style={styles.serviceName}>{item.name}</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Text style={styles.titleText}>دليل الخدمات ☎️</Text>
        <Text style={styles.descriptionText}>
          تواصل بشكل أسرع مع دليل الخدمات الحكومية و الأنشطة التجارية و المتاجر و
          الخدمات العامة
        </Text>
      </View>

      <FlatList
        data={PLACEHOLDER_SERVICES}
        renderItem={renderServiceItem}
        keyExtractor={(item) => item.id}
        numColumns={3}
        style={styles.gridStyle}
        contentContainerStyle={styles.gridContentContainer}
        scrollEnabled={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '90%',
    alignSelf: 'center',
    alignItems: 'center',
    marginTop: '3%',
    marginBottom: '4%',
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 20,
    width: '100%',
  },
  titleText: {
    fontSize: screenWidth * 0.055,
    fontFamily: 'Rubik-ExtraBold',
    color: '#068E77',
    textAlign: 'center',
    writingDirection: 'rtl',
  },
  descriptionText: {
    fontSize: screenWidth * 0.038,
    fontFamily: 'Rubik-Bold',
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginTop: 8,
    width: '90%',
    writingDirection: 'rtl',
    lineHeight: screenWidth * 0.048,
  },
  gridStyle: {
    width: '100%',
  },
  gridContentContainer: {},
  serviceWrapper: {
    flex: 1,
    margin: 5,
    alignItems: 'center',
  },
  serviceItemContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    width: 110,
    height: 110,
    marginBottom: 5,
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  iconImage: {
    width: 75,
    height: 75,
  },
  serviceName: {
    fontFamily: 'Rubik-Bold',
    fontWeight: 'bold',
    fontSize: 12,
    textAlign: 'center',
    color: '#068E77',
    writingDirection: 'rtl',
    lineHeight: 28,
    paddingTop: 16,
  },
});

export default React.memo(HomeServices);