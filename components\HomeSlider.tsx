import * as React from "react";
import { Dimensions, Text, View, Image } from "react-native";
import { useSharedValue } from "react-native-reanimated";
import Carousel, {
  ICarouselInstance,
  Pagination,
} from "react-native-reanimated-carousel";
 
const data = [
  require("../assets/images/Home/homepic1.png"),
  require("../assets/images/Home/homepic2.png"),
  require("../assets/images/Home/homepic3.png"),
  require("../assets/images/Home/homepic4.png"),
];
const width = Dimensions.get("window").width;
 
function HomeSlider() {
  const ref = React.useRef<ICarouselInstance>(null);
  const progress = useSharedValue<number>(0);
  
  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      /**
       * Calculate the difference between the current index and the target index
       * to ensure that the carousel scrolls to the nearest index
       */
      count: index - progress.value,
      animated: true,
    });
  };
 
  return (
    <View style={{ flex: 1 }}>
      <Carousel
        ref={ref}
        width={width - 30}
        height={400}
        data={data}
        loop={true}
        autoPlay={true}
        autoPlayInterval={2000}
        onProgressChange={progress}
        renderItem={({ item, index }) => (
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              borderRadius: 16,
              marginHorizontal: 20,
              marginVertical: 1,
              overflow: "hidden",
            }}
          >
            <Image
              source={item}
              style={{
                width: "100%",
                height: "100%",
                borderRadius: 16,
              }}
              contentFit="cover"
            />
          </View>
        )}
      />
 
      <Pagination.Basic
        progress={progress}
        data={data}
        dotStyle={{ backgroundColor: "rgba(255, 0, 0, 0.44)", borderRadius: 50 , width: 10, height: 10}}
        containerStyle={{ gap: 10, marginTop: 20 , marginBottom: 20}}
        onPress={onPressPagination}
      />
    </View>
  );
}
 
export default HomeSlider;