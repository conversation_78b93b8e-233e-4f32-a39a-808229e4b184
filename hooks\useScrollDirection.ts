import { useEffect, useRef, useState } from 'react';
import { useSharedValue, withSpring, withTiming } from 'react-native-reanimated';

interface ScrollDirectionOptions {
  threshold?: number;
  throttleMs?: number;
  initialDirection?: 'up' | 'down';
}

export interface ScrollDirectionHook {
  scrollDirection: 'up' | 'down';
  isScrolling: boolean;
  scrollY: number;
  headerTranslateY: any; // Animated.SharedValue
  headerOpacity: any; // Animated.SharedValue
  onScroll: (event: any) => void;
}

export const useScrollDirection = (
  options: ScrollDirectionOptions = {}
): ScrollDirectionHook => {
  const {
    threshold = 10,
    throttleMs = 100,
    initialDirection = 'up',
  } = options;

  const [scrollDirection, setScrollDirection] = useState<'up' | 'down'>(initialDirection);
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  // Refs for tracking scroll state
  const lastScrollY = useRef(0);
  const scrollTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
  const lastThrottleTime = useRef(0);

  // Animated values for header animation
  const headerTranslateY = useSharedValue(0);
  const headerOpacity = useSharedValue(1);

  // Throttle function for performance optimization
  const throttle = (func: Function, delay: number) => {
    return (...args: any[]) => {
      const now = Date.now();
      if (now - lastThrottleTime.current >= delay) {
        lastThrottleTime.current = now;
        func(...args);
      }
    };
  };

  // Debounce function for scroll end detection
  const debounce = (func: Function, delay: number) => {
    return (...args: any[]) => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
      scrollTimeout.current = setTimeout(() => func(...args), delay);
    };
  };

  // Handle scroll direction logic
  const handleScrollDirection = (currentScrollY: number) => {
    const scrollDifference = currentScrollY - lastScrollY.current;
    
    // Only update if scroll difference exceeds threshold
    if (Math.abs(scrollDifference) < threshold) {
      return;
    }

    const newDirection = scrollDifference > 0 ? 'down' : 'up';
    
    // Only update if direction actually changed
    if (newDirection !== scrollDirection) {
      setScrollDirection(newDirection);
      
      // Animate header based on direction
      if (newDirection === 'down') {
        // Hide header when scrolling down
        headerTranslateY.value = withSpring(-80, {
          damping: 15,
          stiffness: 150,
        });
        headerOpacity.value = withTiming(0, { duration: 300 });
      } else {
        // Show header when scrolling up
        headerTranslateY.value = withSpring(0, {
          damping: 15,
          stiffness: 150,
        });
        headerOpacity.value = withTiming(1, { duration: 300 });
      }
    }

    lastScrollY.current = currentScrollY;
  };

  // Handle scroll end detection
  const handleScrollEnd = debounce(() => {
    setIsScrolling(false);
  }, 150);

  // Throttled scroll handler
  const throttledScrollHandler = throttle((currentScrollY: number) => {
    setScrollY(currentScrollY);
    handleScrollDirection(currentScrollY);
  }, throttleMs);

  // Main scroll event handler
  const onScroll = (event: any) => {
    const currentScrollY = event.nativeEvent.contentOffset.y;
    
    // Prevent negative scroll values
    if (currentScrollY < 0) return;
    
    setIsScrolling(true);
    throttledScrollHandler(currentScrollY);
    handleScrollEnd();
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, []);

  // Reset header position when reaching top
  useEffect(() => {
    if (scrollY <= threshold) {
      headerTranslateY.value = withSpring(0, {
        damping: 15,
        stiffness: 150,
      });
      headerOpacity.value = withTiming(1, { duration: 300 });
      setScrollDirection('up');
    }
  }, [scrollY, threshold]);

  return {
    scrollDirection,
    isScrolling,
    scrollY,
    headerTranslateY,
    headerOpacity,
    onScroll,
  };
};