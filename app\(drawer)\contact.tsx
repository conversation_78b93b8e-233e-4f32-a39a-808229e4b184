// app/(drawer)/(tabs)/contact.tsx

import { Ionicons } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  Modal,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';

const { width } = Dimensions.get('window');

// Define the form data structure
interface FormData {
  full_name: string;
  phone_number: string;
  address: string;
  email: string;
  problem_type: string;
  problem_description: string;
  image: ImagePicker.ImagePickerAsset | null;
}

// Problem types for the dropdown
const problemTypes = [
  'اقتراح', 'مرافق', 'صحة', 'تعليم وثقافة', 
  'طرق ومواصلات', 'بيئة', 'خدمات أخرى'
];

export default function ContactScreen() {
  const [formData, setFormData] = useState<FormData>({
    full_name: '', phone_number: '', address: '', email: '',
    problem_type: 'اقتراح', problem_description: '', image: null,
  });
  const [focusedInput, setFocusedInput] = useState<string | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  const handleChange = (name: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // --- Native Image Picker Logic ---
  const handleImagePick = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('عذرًا', 'نحتاج إلى إذن للوصول إلى الكاميرا لكي يعمل هذا.');
      return;
    }

    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setFormData(prev => ({ ...prev, image: result.assets[0] }));
    }
  };

  const removeImage = () => {
    setFormData(prev => ({ ...prev, image: null }));
  };

  // --- Native Form Submission Logic ---
  const handleSubmit = async () => {
    if (!formData.full_name || !formData.phone_number || !formData.problem_description) {
      Alert.alert('خطأ', 'يرجى ملء جميع الحقول المطلوبة (*)');
      return;
    }

    const data = new FormData();
    Object.keys(formData).forEach(key => {
      if (key !== 'image' && formData[key as keyof FormData]) {
        data.append(key, formData[key as keyof FormData] as string);
      }
    });

    if (formData.image) {
      const uriParts = formData.image.uri.split('.');
      const fileType = uriParts[uriParts.length - 1];
      const fileName = formData.image.fileName || `photo.${fileType}`;
      
      data.append('image', {
        uri: formData.image.uri,
        name: fileName,
        type: `image/${fileType}`,
      } as any);
    }
    
    try {
      // Replace with your actual API endpoint
      const response = await fetch("http://localhost:3000/complaints", {
        method: 'POST',
        body: data,
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      if (!response.ok) throw new Error('فشل إرسال الطلب');

      Alert.alert('نجاح', 'تم إرسال طلبك بنجاح!');
      // Reset form
      setFormData({
        full_name: '', phone_number: '', address: '', email: '',
        problem_type: 'اقتراح', problem_description: '', image: null,
      });
    } catch (error) {
      console.error("خطأ في إرسال الشكوى:", error);
      Alert.alert('خطأ', 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
    }
  };

  const getDynamicInputStyle = (inputName: string) => [
    styles.input,
    focusedInput === inputName && styles.inputFocused,
  ];

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.formContainer}>
          <View style={styles.header}>
            <Text style={styles.title}>هل تحتاج إلى مساعدة؟</Text>
            <Text style={styles.subtitle}>
              نحن هنا لمساعدتك, يمكنك تقديم شكوى أو مقترح, لأى مشكلة تواجهك داخل مدينة الشيخ زايد
            </Text>
          </View>

          <View style={styles.formGrid}>
            {[
              { label: '*الاسم بالكامل', name: 'full_name', placeholder: 'مثال: محمد أحمد خالد' },
              { label: '*رقم الهاتف المحمول', name: 'phone_number', placeholder: 'مثال: ٠١٢٣٤٥٦٧٨٩٠', keyboardType: 'phone-pad' },
              { label: 'عنوان موقع المشكلة (اختياري)', name: 'address', placeholder: 'مثال: شارع النزهة عمارة ١٩' },
              { label: 'البريد الإلكتروني (اختياري)', name: 'email', placeholder: 'مثال: <EMAIL>', keyboardType: 'email-address' },
            ].map((input, index) => (
              <View style={styles.inputWrapper} key={index}>
                <Text style={styles.label}>{input.label}</Text>
                <TextInput
                  value={formData[input.name as keyof FormData] as string}
                  onChangeText={(text) => handleChange(input.name as keyof FormData, text)}
                  style={getDynamicInputStyle(input.name)}
                  onFocus={() => setFocusedInput(input.name)}
                  onBlur={() => setFocusedInput(null)}
                  placeholder={input.placeholder}
                  placeholderTextColor="#A9A9A9"
                  keyboardType={(input.keyboardType as any) || 'default'}
                />
              </View>
            ))}
            
            {/* Custom Dropdown */}
            <View style={styles.inputWrapper}>
                <Text style={styles.label}>*نوع المشكله - الإقتراح</Text>
                <TouchableOpacity style={styles.input} onPress={() => setModalVisible(true)}>
                    <Text style={{textAlign: 'right'}}>{formData.problem_type}</Text>
                </TouchableOpacity>
            </View>

            {/* Image Picker */}
            <View style={styles.inputWrapper}>
                <Text style={styles.label}>إرفاق صورة (اختياري)</Text>
                <TouchableOpacity style={styles.input} onPress={handleImagePick}>
                    {formData.image ? (
                        <View style={styles.imagePreviewContainer}>
                            <Text style={styles.imageText} numberOfLines={1}>{formData.image.fileName || 'Image'}</Text>
                            <TouchableOpacity onPress={removeImage}>
                                <Ionicons name="close-circle" size={22} color="#F35A30" />
                            </TouchableOpacity>
                        </View>
                    ) : (
                        <View style={styles.imagePreviewContainer}>
                            <Text style={{color: '#A9A9A9'}}>تحميل صورة</Text>
                            <Ionicons name="image-outline" size={22} color="#A9A9A9" />
                        </View>
                    )}
                </TouchableOpacity>
            </View>

            {/* Problem Description */}
            <View style={styles.inputWrapper}>
                <Text style={styles.label}>*المشكلة أو الإقتراح بالتفصيل</Text>
                <TextInput
                    value={formData.problem_description}
                    onChangeText={(text) => handleChange('problem_description', text)}
                    style={[getDynamicInputStyle('problem_description'), styles.textArea]}
                    onFocus={() => setFocusedInput('problem_description')}
                    onBlur={() => setFocusedInput(null)}
                    placeholder="برجاء شرح مشكلتك أو اقتراحك بالتفصيل..."
                    placeholderTextColor="#A9A9A9"
                    multiline
                />
            </View>

            {/* Emergency Info */}
            <View style={styles.inputWrapper}>
                <Text style={styles.emergencyText}>
                    في حالة وجود شكوي عاجلة... 
                    <Text style={{color: '#F35A30'}}> يرجى الاتصال بالرقم الساخن 15100</Text>
                </Text>
            </View>

            {/* Submit Button */}
            <View style={[styles.inputWrapper, {alignItems: 'center'}]}>
                <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
                    <Text style={styles.submitButtonText}>تقديم الطلب</Text>
                </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {problemTypes.map((type) => (
              <TouchableOpacity
                key={type}
                style={styles.modalItem}
                onPress={() => {
                  handleChange('problem_type', type);
                  setModalVisible(false);
                }}
              >
                <Text style={styles.modalItemText}>{type}</Text>
              </TouchableOpacity>
            ))}
            <TouchableOpacity style={[styles.modalItem, styles.cancelButton]} onPress={() => setModalVisible(false)}>
                <Text style={styles.cancelButtonText}>إلغاء</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
    safeArea: { flex: 1, backgroundColor: '#EBDECE',paddingTop: 120 },
    scrollContainer: {  paddingBottom: 40 },
    formContainer: {
        marginHorizontal: '5%',
        padding: '5%',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 32,
    },
    header: { alignItems: 'center', marginBottom: 24 },
    title: {
        fontSize: width * 0.07,
        fontFamily: 'Rubik-Bold',
        color: '#000',
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 14, fontFamily: 'Rubik-Medium', color: '#068E77',
        textAlign: 'center', marginTop: 8, lineHeight: 24,
    },
    // --- UPDATED STYLES FOR SINGLE COLUMN LAYOUT ---
    formGrid: {
        width: '100%',
    },
    inputWrapper: {
        width: '100%', // Each input group takes the full width
        marginBottom: 20, // Increased margin for better spacing
    },
    // --- END OF UPDATED STYLES ---
    label: {
        fontSize: width * 0.04, fontFamily: 'Rubik-Medium', color: '#F35A30',
        textAlign: 'right', marginBottom: 8,
    },
    input: {
        backgroundColor: 'rgba(52, 158, 137, 0.5)',
        height: 50,
        borderRadius: 8,
        paddingHorizontal: 12,
        textAlign: 'right',
        fontFamily: 'Rubik-Regular',
        fontSize: 14,
        color: '#333',
        justifyContent: 'center',
    },
    inputFocused: {
        backgroundColor: 'white',
        borderColor: '#068E77',
        borderWidth: 1.5,
    },
    textArea: { height: 150, textAlignVertical: 'top', paddingTop: 12 },
    imagePreviewContainer: {
        flexDirection: 'row-reverse',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    imageText: { flex: 1, textAlign: 'right', marginRight: 8 },
    emergencyText: { textAlign: 'center', fontFamily: 'Rubik-Medium', color: '#565656', lineHeight: 22 },
    submitButton: {
        width: '70%', // Slightly wider button for single column layout
        backgroundColor: '#F35A30',
        paddingVertical: 14,
        borderRadius: 10,
        marginTop: 16,
    },
    submitButtonText: {
        color: 'white',
        textAlign: 'center',
        fontFamily: 'Rubik-Bold',
        fontSize: 16,
    },
    modalContainer: {
        flex: 1,
        justifyContent: 'flex-end',
        backgroundColor: 'rgba(0,0,0,0.5)',
    },
    modalContent: {
        backgroundColor: 'white',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        padding: 16,
    },
    modalItem: { paddingVertical: 16, borderBottomWidth: 1, borderBottomColor: '#eee' },
    modalItemText: { textAlign: 'center', fontSize: 18, fontFamily: 'Rubik-Regular' },
    cancelButton: { borderBottomWidth: 0, marginTop: 10 },
    cancelButtonText: { textAlign: 'center', fontSize: 18, fontFamily: 'Rubik-Bold', color: '#F35A30' },
});