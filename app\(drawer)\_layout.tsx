// app/(drawer)/_layout.tsx
import { CustomDrawerContent } from '@/components/CustomDrawer';
import HomeHeader from '@/components/HomeHeader';
import { Drawer } from 'expo-router/drawer';

export default function DrawerLayout() {
  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        header: () => <HomeHeader />,
        headerShown: true,
        drawerType: 'slide',
        drawerPosition: 'right', // RTL layout
        overlayColor: 'rgba(0, 0, 0, 0.3)',
        drawerStyle: {
          width: '75%',
          backgroundColor: 'transparent',
        },
      }}
    >
      <Drawer.Screen 
        name="(tabs)" 
        options={{
          drawerLabel: 'الرئيسية',
          title: 'الرئيسية',
        }}
      />
      <Drawer.Screen 
        name="about" 
        options={{
          drawerLabel: 'حول المدينة',
          title: 'حول المدينة',
        }}
      />
       <Drawer.Screen 
        name="notifications" 
        options={{
          drawerLabel: 'الإشعارات',
          title: 'الإشعارات',
        }}
      />
      <Drawer.Screen 
        name="contact" 
        options={{
          drawerLabel: 'اتصل بنا',
          title: 'اتصل بنا',
        }}
      />
      <Drawer.Screen 
        name="settings" 
        options={{
          drawerLabel: 'الإعدادات',
          title: 'الإعدادات',
        }}
      />
    </Drawer>
  );
}