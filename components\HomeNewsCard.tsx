// components/HomeNewsCard.tsx

import React from 'react';
import {
  Dimensions,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

interface HomeNewsCardProps {
  id: string;
  title: string;
  date: string;
  image: any;
  type: string;
  onPress: () => void;
}

const { width } = Dimensions.get('window');

const HomeNewsCard: React.FC<HomeNewsCardProps> = ({
  title,
  date,
  image,
  type,
  onPress,
}) => {
  return (
    <TouchableOpacity onPress={onPress} style={styles.card} activeOpacity={0.9}>
      {/* Image is now a separate element at the top, like in FullNewsCard */}
      <Image source={image} style={styles.image} contentFit="cover" />
      
      {/* This container holds all the text content below the image */}
      <View style={styles.contentContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {title}
        </Text>
        <View style={styles.bottomRow}>
          <Text style={styles.date}>{date}</Text>
          <View style={styles.tagContainer}>
            <Text style={styles.tagText}>{type}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    width: width * 0.7, // Adjust width as needed for the carousel
    height: 350, // Fixed height for the card
    backgroundColor: 'white',
    borderRadius: 16,
    overflow: 'hidden', // Ensures the image corners are rounded
    marginHorizontal: 10,
  },
  image: {
    width: '100%',
    height: 150, // Fixed height for the image
  },
  contentContainer: {
    padding: 20,
    flex: 1, // Use flex to push the bottom row down
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 17,
    fontFamily: 'Rubik-medium',
    color: '#333',
    textAlign: 'right',
    lineHeight: 22,
    writingDirection: 'rtl',
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 10, // Add some space above the bottom row
  },
  date: {
    fontSize: 14,
    fontFamily: 'Rubik-Regular',
    color: '#666',
  },
  tagContainer: {
    backgroundColor: '#3b5998', // A blueish-purple color from your design
    paddingVertical: 2,
    paddingHorizontal: 25,
    borderRadius: 25,
  },
  tagText: {
    color: 'white',
    fontSize: 12,
    fontFamily: 'Rubik-Bold',
  },
});

export default React.memo(HomeNewsCard);