// app/(drawer)/(tabs)/news.tsx

import FilterDropdown from '@/components/FilterDropdown';
import NewsCarouselCard from '@/components/NewsCarouselCard';
import { useRouter } from 'expo-router';
import React, { useMemo, useRef, useState } from 'react';
import {
  Dimensions,
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import Carousel, { ICarouselInstance } from 'react-native-reanimated-carousel';

const { width } = Dimensions.get('window');

// --- Placeholder Data with Categories ---
const placeholderNews = [
    {
        id: '1',
        title: 'افتتاح حديقة الشيخ زايد المركزية بعد التجديدات',
        date: '2025-07-10T10:00:00.000Z',
        description: 'شهدت المدينة افتتاح الحديقة المركزية بحلتها الجديدة، مع إضافة مناطق ألعاب ومساحات خضراء واسعة للعائلات.',
        image: require('@/assets/images/event1.jpg'),
        category: 'أخبار المدينة',
    },
    {
        id: '2',
        title: 'تأجيل القرعة العلنية لطرح 30 وحدة بمجمع الورش',
        date: '2024-12-20T10:00:00.000Z',
        description: 'نظراً للإقبال الشديد، تم تأجيل القرعة العلنية للوحدات الصناعية لضمان الشفافية وتكافؤ الفرص.',
        image: require('@/assets/images/event2.jpg'),
        category: 'إعلانات',
    },
    {
        id: '3',
        title: 'إطلاق المرحلة الجديدة من مشروع الإسكان الشبابي',
        date: '2024-11-15T10:00:00.000Z',
        description: 'أعلنت وزارة الإسكان عن بدء التسجيل في المرحلة الجديدة من الوحدات السكنية المخصصة للشباب بأسعار مدعومة.',
        image: require('@/assets/images/event3.jpg'),
        category: 'مشروعات',
    },
];

const years = [
  { label: 'السنة', value: 'all' },
  { label: '2025', value: 2025 },
  { label: '2024', value: 2024 },
];

const months = [
  { label: 'الشهر', value: 'all' },
  ...Array.from({ length: 12 }, (_, i) => ({ label: `${i + 1}`, value: i })),
];

export default function NewsScreen() {
  const router = useRouter();
  const carouselRef = useRef<ICarouselInstance>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const [selectedYear, setSelectedYear] = useState<string | number>('all');
  const [selectedMonth, setSelectedMonth] = useState<string | number>('all');
  
  const filteredData = useMemo(() => {
    return placeholderNews.filter(item => {
      const itemDate = new Date(item.date);
      const isYearMatch = selectedYear === 'all' || itemDate.getFullYear() === selectedYear;
      const isMonthMatch = selectedMonth === 'all' || itemDate.getMonth() === selectedMonth;
      return isYearMatch && isMonthMatch;
    });
  }, [selectedYear, selectedMonth]);

  const handleCardPress = (id: string) => {
    router.push(`/(drawer)/news/${id}`);
  };

  const Pagination = () => (
    <View style={styles.paginationContainer}>
      <Text style={styles.paginationLabel}>التالي</Text>
      {filteredData.map((_, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => carouselRef.current?.scrollTo({ index, animated: true })}
          style={[styles.paginationDot, activeIndex === index && styles.paginationDotActive]}
        >
          <Text style={[styles.paginationNumber, activeIndex === index && styles.paginationNumberActive]}>
            {index + 1}
          </Text>
        </TouchableOpacity>
      ))}
      <Text style={styles.paginationLabel}>السابق</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <Text style={styles.title}>أخبار المدينة ✨</Text>
      </View>

      <View style={styles.filtersRow}>
        <FilterDropdown
          placeholder="السنة"
          options={years}
          selectedValue={selectedYear}
          onSelect={(val) => setSelectedYear(val)}
        />
        <FilterDropdown
          placeholder="الشهر"
          options={months}
          selectedValue={selectedMonth}
          onSelect={(val) => setSelectedMonth(val)}
        />
      </View>

      <View style={styles.carouselWrapper}>
        <View style={styles.carouselContainer}>
          {filteredData.length > 0 ? (
            <Carousel
              ref={carouselRef}
              loop={false}
              width={width}
              height={500}
              data={filteredData}
              onSnapToItem={(index) => setActiveIndex(index)}
              renderItem={({ item }) => (
                <NewsCarouselCard
                  item={item}
                  onPress={() => handleCardPress(item.id)}
                />
              )}
            />
          ) : (
            <Text style={styles.noResultsText}>لا توجد أخبار تطابق هذا البحث.</Text>
          )}
        </View>
      </View>
      
      {filteredData.length > 1 && <Pagination />}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#EBDECE',
  },
  header: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 75,
  },
  title: {
    fontSize: width * 0.06,
    fontFamily: 'Rubik-Bold',
    color: '#F35A30',
    marginLeft: 10,
  },
  headerIcon: {
    width: 32,
    height: 32,
    tintColor: '#F35A30',
  },
  filtersRow: {
    flexDirection: 'row-reverse',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
    marginBottom: 10,
  },
  carouselWrapper: {
    backgroundColor: '#FFFFFF',
    borderRadius: 30,
    marginTop: 5,
    marginBottom: 20,
    marginHorizontal: 15,
    shadowColor: '#ebdecebf',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 3,
    elevation: 5,
  },
  carouselContainer: {
    height: 550,
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 15,
  },
  paginationDot: {
    width: 32,
    height: 32,
    borderRadius: 14,
    backgroundColor: '#FBF8F5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  paginationDotActive: {
    backgroundColor: '#F35A30',
  },
  paginationLabel: {
    writingDirection: 'rtl',
    fontFamily: 'Rubik-Medium',
    fontSize: 12,
    color: '#797979',
    width: 75,
    height: 33,
    borderRadius: 18,
    backgroundColor: '#FBF8F5',
    textAlign: 'center',
  },
  paginationNumber: {
    fontFamily: 'Rubik-Bold',
    fontSize: 14,
    color: '#666',
  },
  paginationNumberActive: {
    color: 'white',
  },
  noResultsText: {
    width: '100%',
    textAlign: 'center',
    fontSize: 16,
    color: '#888',
    fontFamily: 'Rubik-Medium',
    marginTop: 40,
  },
});