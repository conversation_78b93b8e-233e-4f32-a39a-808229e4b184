import React, { useEffect, useState } from 'react';
import {
  Dimensions,
  I18nManager,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming
} from 'react-native-reanimated';
import HomeSlider from './HomeSlider';
import { TypingText } from './TypingText';

const { width } = Dimensions.get('window');

interface AboutZayedHomeProps {
  onLearnMorePress?: () => void;
}

const AboutZayedHome: React.FC<AboutZayedHomeProps> = ({ onLearnMorePress }) => {
  // Typing animation state
  const [displayedText, setDisplayedText] = useState('');
  const [showCursor, setShowCursor] = useState(true);
  const fullText = 'مدينة السعادة';

  // Cursor blinking animation
  const cursorOpacity = useSharedValue(1);

  useEffect(() => {
    // Enable RTL layout
    I18nManager.allowRTL(true);
    I18nManager.forceRTL(true);

    // Typing animation
    let currentIndex = 0;
    const typingInterval = setInterval(() => {
      if (currentIndex <= fullText.length) {
        setDisplayedText(fullText.slice(0, currentIndex));
        currentIndex++;
      } else {
        clearInterval(typingInterval);
        // Keep cursor visible and blinking after typing is complete
        setShowCursor(true);
      }
    }, 80); // 80ms delay to match original typeSpeed

    return () => {
      clearInterval(typingInterval);
    };
  }, []);

  // Start cursor blinking animation after component mounts
  useEffect(() => {
    cursorOpacity.value = withRepeat(
      withTiming(0, { duration: 500 }),
      -1,
      true
    );
  }, []);

  const cursorAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: cursorOpacity.value,
    };
  });

  const handleLearnMorePress = () => {
    if (onLearnMorePress) {
      onLearnMorePress();
    } else {
      // Navigate to about page or show more info
      console.log('Navigate to about page');
      // router.push('/about'); // Uncomment when about page exists
    }
  };

  return (
    <View style={styles.container}>
      <HomeSlider /> 
      {/* Title */}
      <Text style={styles.title}>مدينة الشيخ زايد📌</Text>

      {/* Animated Text Section */}
      <View style={styles.animatedTextContainer}>
        <TypingText />
        <Text style={styles.subtitle}>
          كما يطلق عليها سكانها و روادها من كل مكان
        </Text>
      </View>

      {/* Learn More Button */}
      <TouchableOpacity
        style={styles.button}
        onPress={handleLearnMorePress}
        activeOpacity={0.8}
      >
        <Text style={styles.buttonText}>المزيد عن المدينة</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 24,

    borderRadius: 16,
    minHeight: 200,
  },
  title: {
    fontFamily: 'Rubik-Bold',
    fontSize: 23,
    textAlign: 'center',
    color: '#000000',
    marginBottom: 16,
    writingDirection: 'rtl',
  },
  animatedTextContainer: {
    width: '100%',
    fontFamily: 'Rubik-Bold',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  typedTextWrapper: {
    alignItems: 'center',
    marginBottom: 12,
  },
  cursor: {
    fontWeight: '800',
    fontFamily: 'Rubik-Bold',
    fontSize: width * 0.08,
    color: '#F35A30',
  },
  subtitle: {
    fontWeight: '500',
    fontFamily: 'Rubik-Medium',
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    writingDirection: 'rtl',
    lineHeight: width * 0.05,
  },
  button: {
    width: '70%',
    backgroundColor: '#F35A30',
    borderRadius: 29,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: '#ffffff',
    fontFamily: 'Rubik-Bold',
    fontSize: 16,
    textAlign: 'center',
  },
});

export default React.memo(AboutZayedHome);
