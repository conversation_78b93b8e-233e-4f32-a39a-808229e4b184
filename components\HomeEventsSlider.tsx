import * as React from "react";
import { Dimensions, Image, View,StyleSheet } from "react-native";
import { useSharedValue } from "react-native-reanimated";
import Carousel, {
  ICarouselInstance,
  Pagination,
} from "react-native-reanimated-carousel";
 
const data = [
  require("@/assets/images/event1.jpg"),
  require("@/assets/images/event2.jpg"),
  require("@/assets/images/event3.jpg"),
];
const width = Dimensions.get("window").width;
 
function HomeEventsSlider() {
  const ref = React.useRef<ICarouselInstance>(null);
  const progress = useSharedValue<number>(0);
  
  const onPressPagination = (index: number) => {
    ref.current?.scrollTo({
      /**
       * Calculate the difference between the current index and the target index
       * to ensure that the carousel scrolls to the nearest index
       */
      count: index - progress.value,  
      animated: false, // Add this line to animate the scroll, set to false to disable animation.
    });
  };
 
  return (
    <View style={{ flex: 1 }}>
      <Carousel
        ref={ref}
        width={width}
        height={160}
        data={data}
        loop={true}
        autoPlay={true}
        autoPlayInterval={2000}
        onProgressChange={progress}
        renderItem={({ item, index }) => (
  <View
    style={{
      flex: 1,
      justifyContent: 'center',
      // This part is correct
      borderRadius: 12,
      marginHorizontal: 20,
      overflow: 'hidden',
    }}>
    <Image
      source={item}
      style={{
        width: '100%',
        height: '100%',
        borderRadius: 12,
        ...StyleSheet.absoluteFillObject, // This ensures the image fills the container

      }}
      contentFit="cover"
    />
  </View>
)}
      />
 
      <Pagination.Basic
        progress={progress}
        data={data}
        dotStyle={{ backgroundColor: "#068E77", borderRadius: 50 , width: 10, height: 10}}
        containerStyle={{ gap: 10, marginTop: 20 , marginBottom: 20}}
        onPress={onPressPagination}
      />
    </View>
  );
}
 
export default HomeEventsSlider;