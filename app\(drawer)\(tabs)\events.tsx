// app/(drawer)/(tabs)/news.tsx

import FullNewsCard from '@/components/FullNewsCard'; // Import the new card component
import { useRouter } from 'expo-router';
import { useState } from 'react';
import {
  Dimensions,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';

const { width } = Dimensions.get('window');

// --- Placeholder Data ---
// In a real app, you would fetch this data using Redux or another state management library.
// Using ISO date format (YYYY-MM-DD) makes native Date parsing reliable.
const placeholderNews = [
    {
        id: '1',
        title: 'افتتاح حديقة الشيخ زايد المركزية بعد التجديدات',
        date: '2025-07-10T10:00:00.000Z',
        description: 'شهدت المدينة افتتاح الحديقة المركزية بحلتها الجديدة، مع إضافة مناطق ألعاب ومساحات خضراء واسعة للعائلات.',
        image: require('@/assets/images/event1.jpg'),
    },
    {
        id: '2',
        title: 'ماراثون زايد الخيري يجمع الآلاف لدعم القضايا الإنسانية',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
        description: 'شارك أكثر من 5000 عداء في الماراثون السنوي الذي يهدف إلى جمع التبرعات لدعم المستشفيات والمبادرات الصحية.',
        image: require('@/assets/images/event2.jpg'),
    },
    {
        id: '3',
        title: 'إطلاق المرحلة الجديدة من مشروع الإسكان الشبابي',
        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
        description: 'أعلنت وزارة الإسكان عن بدء التسجيل في المرحلة الجديدة من الوحدات السكنية المخصصة للشباب بأسعار مدعومة.',
        image: require('@/assets/images/event3.jpg'),
    },
    {
        id: '4',
        title: 'تطوير شبكة الطرق والمواصلات لتسهيل الحركة المرورية',
        date: '2025-06-25T15:00:00.000Z',
        description: 'بدأت أعمال توسعة وتطوير المحاور الرئيسية في المدينة لتقليل الازدحام المروري وتحسين جودة الطرق.',
        image: require('@/assets/images/Home/homepic4.png'),
    },
     {
        id: '5',
        title: 'مهرجان التسوق السنوي ينطلق بعروض وخصومات مذهلة',
        date: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(), // 40 days ago
        description: 'انطلق مهرجان التسوق في جميع المراكز التجارية بالمدينة، ويستمر لمدة شهر كامل مع فعاليات ترفيهية يومية.',
        image: require('@/assets/images/Home/homepic1.png'),
    },
];

type FilterOption = 'يومي' | 'أسبوعي' | 'شهري' | 'سنوي';

// Filter Button Component
const FilterButton = ({
  label,
  isSelected,
  onPress,
}: {
  label: string;
  isSelected: boolean;
  onPress: () => void;
}) => (
  <TouchableOpacity
    style={[styles.filterButton, isSelected && styles.filterButtonSelected]}
    onPress={onPress}
  >
    <Text style={[styles.filterButtonText, isSelected && styles.filterButtonTextSelected]}>
      {label}
    </Text>
  </TouchableOpacity>
);

export default function EventsScreen() {
  const router = useRouter();
  const [selectedOption, setSelectedOption] = useState<FilterOption>('سنوي');

  const filterActivities = () => {
    const now = new Date();
    return placeholderNews.filter((activity) => {
      const eventDate = new Date(activity.date);

      switch (selectedOption) {
        case 'سنوي':
          return eventDate.getFullYear() === now.getFullYear();
        case 'شهري':
          return (
            eventDate.getFullYear() === now.getFullYear() &&
            eventDate.getMonth() === now.getMonth()
          );
        case 'أسبوعي':
          const oneWeekAgo = new Date();
          oneWeekAgo.setDate(now.getDate() - 7);
          return eventDate >= oneWeekAgo && eventDate <= now;
        case 'يومي':
          return (
            eventDate.getFullYear() === now.getFullYear() &&
            eventDate.getMonth() === now.getMonth() &&
            eventDate.getDate() === now.getDate()
          );
        default:
          return true;
      }
    });
  };
  
  const filteredData = filterActivities();

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView contentContainerStyle={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>أحدث الفعاليات ✨
          </Text>
          <Text style={styles.subtitle}>
            استمتع بتجربة فريدة و ممتعة وشارك بأحدث الفعاليات و الحفلات التى تقام
            طوال العام مع الأصدقاء و العائلة بمدينة السعادة مدينة الشيخ زايد.
          </Text>
        </View>

        <View style={styles.filterContainer}>
          {(['يومي', 'أسبوعي', 'شهري', 'سنوي'] as FilterOption[]).map((option) => (
            <FilterButton
              key={option}
              label={option}
              isSelected={selectedOption === option}
              onPress={() => setSelectedOption(option)}
            />
          ))}
        </View>

        <View style={styles.gridContainer}>
          {filteredData.length > 0 ? (
            filteredData.map(item => (
              <View key={item.id} style={styles.cardWrapper}>
                <FullNewsCard
                  id={item.id}
                  title={item.title}
                  date={new Date(item.date).toLocaleDateString('ar-EG')}
                  description={item.description}
                  image={item.image}
                  onPress={() => console.log(`Card ${item.id} pressed`)}
                />
              </View>
            ))
          ) : (
            <Text style={styles.noResultsText}>لا توجد فعاليات تطابق هذا الفلتر.</Text>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#EBDECE',
  },
  container: {
    flexGrow: 1,
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 100, // Adjust for header
    paddingBottom: 40,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: width * 0.06,
    fontFamily: 'Rubik-Bold',
    color: '#068E77',
  },
  subtitle: {
    fontSize: width * 0.038,
    fontFamily: 'Rubik-Regular',
    textAlign: 'center',
    color: '#555',
    marginTop: 8,
    lineHeight: 22,
  },
  filterContainer: {
    flexDirection: 'row-reverse', // RTL layout for buttons
    justifyContent: 'space-around',
    width: '100%',
    marginBottom: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderRadius: 30,
    paddingVertical: 8,
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: 'transparent',
  },
  filterButtonSelected: {
    backgroundColor: '#F35A30',
  },
  filterButtonText: {
    color: '#333',
    fontFamily: 'Rubik-Medium',
    fontSize: 14,
  },
  filterButtonTextSelected: {
    color: 'white',
    fontFamily: 'Rubik-Bold',
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  cardWrapper: {
    width: '48%', // Two columns with a small gap
    marginBottom: 16,
  },
  noResultsText: {
    width: '100%',
    textAlign: 'center',
    fontSize: 16,
    color: '#888',
    fontFamily: 'Rubik-Medium',
    marginTop: 40,
  },
});