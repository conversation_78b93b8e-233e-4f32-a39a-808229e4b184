// components/HomeHeader.tsx
import { DrawerActions, useNavigation } from '@react-navigation/native';
import { BlurView } from 'expo-blur';
import { Image } from 'expo-image';
import { router } from 'expo-router'; // Import router
import React from 'react';
import { StatusBar, StyleSheet, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const HomeHeader: React.FC = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();

  const handleMenuPress = () => {
    navigation.dispatch(DrawerActions.toggleDrawer());
  };

  // Navigate to the notifications screen
  const handleNotificationPress = () => {
    router.push('/(drawer)/notifications');
  };

  return (
    <>
      <StatusBar style="light" />
      <View style={[styles.container, { paddingTop: insets.top }]}>
        <BlurView
          intensity={9.2}
          tint="systemUltraThinMaterialLight"
          style={styles.blurView}
          experimentalBlurMethod='dimezisBlurView'
          blurReductionFactor={5}
        >
          <View style={styles.colorOverlay} />
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Image
                source={require('@/assets/images/Logo.svg')}
                style={styles.logo}
                contentFit="contain"
              />
            </View>
            <View style={styles.rightIconsContainer}>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={handleNotificationPress} // Updated onPress
                activeOpacity={0.7}
              >
                <Image
                  source={require('@/assets/images/notification.svg')}
                  style={styles.rightIcon}
                  contentFit="contain"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconButton}
                onPress={handleMenuPress}
                activeOpacity={0.7}
              >
                <Image
                  source={require('@/assets/images/RightMenu.svg')}
                  style={styles.rightIcon}
                  contentFit="contain"
                />
              </TouchableOpacity>
            </View>
          </View>
        </BlurView>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    backgroundColor: '#068e778d',
    overflow: 'hidden',
  },
  blurView: {
    width: '100%',
    height: '100%',
  },
  colorOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#068e778d',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    height: 70,
    backgroundColor: 'transparent',
  },
  logoContainer: {
    flex: 1,
    alignItems: 'flex-start',
    justifyContent: 'center',
  },
  logo: {
    width: 89,
    height: 32,
  },
  rightIconsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  iconButton: {
    padding: 8,
  },
  rightIcon: {
    width: 24,
    height: 24,
  },
});

export default HomeHeader;