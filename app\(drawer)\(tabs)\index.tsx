// app/(drawer)/(tabs)/index.tsx

import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';

import AboutZayedHome from '@/components/AboutZayedHome';
import HelpBanner from '@/components/HelpBanner';
import HomeEvents from '@/components/HomeEvents';
import HomeNews from '@/components/HomeNews';
import HomeServices from '@/components/HomeServices';

// Define your home screen sections as data
const homeSections = [
  { id: 'about', component: <AboutZayedHome /> },
  { id: 'events', component: <HomeEvents /> },
  { id: 'services', component: <HomeServices /> },
  { id: 'help', component: <HelpBanner /> },
  { id: 'news', component: <HomeNews /> },
];

export default function HomeScreen() {
  // Render item function for FlatList
  const renderSection = ({ item }: { item: (typeof homeSections)[0] }) => {
    // Apply different styling based on the component
    let style = styles.dropshadowSlim;
    if (item.id === 'about') {
      style = styles.dropshadow;
    } else if (item.id === 'services') {
      style = styles.dropshadowSlimHidden;
    }
    
    return <View style={style}>{item.component}</View>;
  };

  return (
    <View style={styles.screenContainer}>
      <FlatList
        data={homeSections}
        renderItem={renderSection}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={<View style={styles.bottomSpacer} />} // For extra space at the end
      />
    </View>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: '#ebdecebf',
  },
  contentContainer: {
    paddingTop: 90,
    paddingBottom: 20,
  },
  dropshadow: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: 30,
    marginTop: 5,
    marginBottom: 20,
    marginHorizontal: 15, // Use marginHorizontal for consistency
    shadowColor: '#ebdecebf',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 3,
    elevation: 5,
  },
  dropshadowSlim: {
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginTop: 5,
    marginBottom: 20,
    shadowColor: '#ebdecebf',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 3,
    elevation: 5,
  },
  dropshadowSlimHidden: {
    backgroundColor: 'transparent',
    marginTop: 5,
    marginBottom: 20,
  },
  bottomSpacer: {
    height: 50,
  },
});