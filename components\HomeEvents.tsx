import { useNavigation } from '@react-navigation/native';
import React from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import HomeEventsSlider from './HomeEventsSlider';

// Get screen dimensions for responsive design
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');


const HomeEvents = () => {
  const navigation = useNavigation();
  return (
    <View style={styles.outerContainer}>
        <View style={styles.headerContainer}>
          <Text style={styles.titleText}>✨ أحدث الفاعليات</Text>
          <Text style={styles.descriptionText}>
            استمتع بتجربة فريدة و ممتعة وشارك بأحدث الفاعليات التى تقام طوال العام
            مع الأصدقاء و العائلة بمدينة السعادة مدينة الشيخ زايد
          </Text>
        </View>

        <HomeEventsSlider/>

        <TouchableOpacity
          style={styles.buttonContainer}
          onPress={() =>{
            console.log('Learn more about Zayed City Events pressed');
          }}
        >
          <Text style={styles.buttonText}>عرض الكل</Text>
        </TouchableOpacity>
    </View>
  );
};

// --- StyleSheet (No changes needed here) ---
const styles = StyleSheet.create({
  outerContainer: {
    width: '100%',
    alignSelf: 'center',
    marginTop: '5%',
    borderRadius: 32,
    overflow: 'hidden',
  },
  gradientContainer: {
    padding: '4%',
    alignItems: 'center',
  },
  absolute: {
    position: 'absolute',
    top: 0,
    left: 0,
    bottom: 0,
    right: 0,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  titleText: {
    fontFamily: 'Rubik-Bold',
    fontSize: 22,
    textAlign: 'center',
    color: '#000000',
  },
  descriptionText: {
    fontFamily: 'Rubik-Regular',
    fontSize: 15,
    textAlign: 'center',
    color: '#000000',
    marginTop: 8,
    width: '90%',
  },
  buttonContainer: {
    marginTop: 20,
    width: '60%',
    backgroundColor: '#F35A30',
    borderRadius: 29,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 20,
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontFamily: 'Rubik-Bold',
    fontSize: 17,
    color: 'white',
  },
});

export default React.memo(HomeEvents);