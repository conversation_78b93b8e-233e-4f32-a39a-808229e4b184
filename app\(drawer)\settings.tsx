// app/(drawer)/settings.tsx

import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import {
  Alert,
  Dimensions,
  SafeAreaView,
  ScrollView,
  Share,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width } = Dimensions.get('window');

// --- Placeholder User Data ---
const user = {
  name: 'زائر المدينة',
  email: '<EMAIL>',
  avatar: require('@/assets/images/icon.png'),
};

export default function SettingsScreen() {
  const router = useRouter();
  const [toggles, setToggles] = useState({
    notifications: true,
    darkMode: false,
  });

  const handleToggle = (id: keyof typeof toggles) => {
    setToggles(prev => ({ ...prev, [id]: !prev[id] }));
  };

  // --- Actions for new buttons ---
  const handleShareApp = async () => {
    try {
      await Share.share({
        message: 'تحقق من تطبيق مدينة الشيخ زايد الرسمي! https://expo.dev',
      });
    } catch (error) {
      Alert.alert('خطأ', 'لم نتمكن من فتح قائمة المشاركة.');
    }
  };

  const handleRateApp = () => {
    // This would typically open the App Store or Google Play Store URL
    Alert.alert('تقييم التطبيق', 'سيتم فتح صفحة التطبيق في المتجر قريبًا!');
  };

  const handleClearCache = () => {
    Alert.alert(
      'مسح الذاكرة المؤقتة',
      'هل أنت متأكد أنك تريد مسح ذاكرة التخزين المؤقت؟',
      [
        { text: 'إلغاء', style: 'cancel' },
        { text: 'مسح', onPress: () => console.log('Cache cleared!') },
      ]
    );
  };
  
  // --- Updated Settings Sections ---
  const settingsSections = [
    {
      header: 'الحساب',
      items: [
        { icon: 'key-outline', title: 'تغيير كلمة المرور', type: 'navigation', onPress: () => router.push('/(drawer)/settings/change-password') },
        { icon: 'people-outline', title: 'الحسابات الاجتماعية', subtitle: 'ربط حساباتك', type: 'navigation', onPress: () => console.log('Navigate to Social Accounts') },
      ],
    },
    {
      header: 'التفضيلات',
      items: [
        { icon: 'notifications-outline', title: 'الإشعارات', subtitle: 'تلقي إشعارات الأخبار والفعاليات', type: 'switch', id: 'notifications' },
        { icon: 'moon-outline', title: 'الوضع المظلم', subtitle: 'تفعيل الوضع المظلم للتطبيق', type: 'switch', id: 'darkMode' },
        { icon: 'language-outline', title: 'اللغة', subtitle: 'العربية', type: 'navigation', onPress: () => console.log('Navigate to Language Settings') },
        { icon: 'trash-outline', title: 'مسح ذاكرة التخزين المؤقت', subtitle: 'إزالة البيانات المؤقتة', type: 'navigation', onPress: handleClearCache },
      ],
    },
    {
      header: 'الدعم والمساعدة',
      items: [
        { icon: 'help-circle-outline', title: 'الدعم الفني', subtitle: 'تواصل مع فريق الدعم', type: 'navigation', onPress: () => router.push('/(drawer)/contact') },
        { icon: 'star-outline', title: 'تقييم التطبيق', subtitle: 'عبر عن رأيك في المتجر', type: 'navigation', onPress: handleRateApp },
        { icon: 'share-social-outline', title: 'مشاركة التطبيق', subtitle: 'شارك التطبيق مع أصدقائك', type: 'navigation', onPress: handleShareApp },
      ],
    },
    {
      header: 'حول التطبيق',
      items: [
        { icon: 'information-circle-outline', title: 'عن التطبيق', subtitle: 'معلومات التطبيق والإصدار', type: 'navigation', onPress: () => router.push('/(drawer)/about') },
        { icon: 'shield-checkmark-outline', title: 'سياسة الخصوصية', subtitle: 'اطلع على سياسة الخصوصية', type: 'navigation', onPress: () => console.log('Navigate to Privacy Policy') },
      ],
    },
  ];

  const renderSettingItem = (item: any) => {
    return (
      <TouchableOpacity
        key={item.title}
        style={styles.settingRow}
        onPress={item.onPress}
        disabled={item.type === 'switch'}
        activeOpacity={0.7}
      >
        <View style={styles.settingLeftContent}>
          {item.type === 'switch' ? (
            <Switch
              value={toggles[item.id as keyof typeof toggles]}
              onValueChange={() => handleToggle(item.id as keyof typeof toggles)}
              trackColor={{ false: '#ccc', true: '#068E77' }}
              thumbColor={toggles[item.id as keyof typeof toggles] ? '#fff' : '#f4f3f4'}
            />
          ) : (
            <Ionicons name="chevron-back" size={20} color="#ccc" />
          )}
        </View>
        <View style={styles.settingRightContent}>
          <View style={styles.iconContainer}>
            <Ionicons name={item.icon as any} size={24} color="#068E77" />
          </View>
          <View style={styles.settingTextContainer}>
            <Text style={styles.settingTitle}>{item.title}</Text>
            {item.subtitle && <Text style={styles.settingSubtitle}>{item.subtitle}</Text>}
          </View>
        </View>
      </TouchableOpacity>
    );
  };
  
  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContainer}>
        <View style={styles.profileContainer}>
          <Image source={user.avatar} style={styles.profileImage} />
          <Text style={styles.profileName}>{user.name}</Text>
          <Text style={styles.profileEmail}>{user.email}</Text>
          <TouchableOpacity style={styles.editButton} onPress={() => console.log('Edit Profile Pressed')}>
            <Text style={styles.editButtonText}>تعديل الملف الشخصي</Text>
          </TouchableOpacity>
        </View>

        {settingsSections.map(section => (
          <View key={section.header} style={styles.sectionContainer}>
            <Text style={styles.sectionHeader}>{section.header}</Text>
            <View style={styles.sectionCard}>
              {section.items.map(item => renderSettingItem(item))}
            </View>
          </View>
        ))}

        <TouchableOpacity style={styles.logoutButton} onPress={() => Alert.alert('تسجيل الخروج', 'هل أنت متأكد؟')}>
          <Text style={styles.logoutButtonText}>تسجيل الخروج</Text>
        </TouchableOpacity>

        <View style={styles.footer}>
          <Text style={styles.versionText}>الإصدار 1.0.0</Text>
          <Text style={styles.copyrightText}>© 2025 مدينة الشيخ زايد. جميع الحقوق محفوظة.</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#EBDECE',
    paddingTop: 100,
  },
  scrollContainer: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 32,
    paddingHorizontal: 20,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: '#068E77',
  },
  profileName: {
    fontSize: 22,
    fontFamily: 'Rubik-Bold',
    color: '#333',
    marginTop: 12,
  },
  profileEmail: {
    fontSize: 16,
    fontFamily: 'Rubik-Regular',
    color: '#666',
    marginTop: 4,
  },
  editButton: {
    marginTop: 16,
    backgroundColor: '#068E771A',
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 20,
  },
  editButtonText: {
    color: '#068E77',
    fontFamily: 'Rubik-Medium',
    fontSize: 14,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionHeader: {
    fontSize: 16,
    fontFamily: 'Rubik-Bold',
    color: '#888',
    textAlign: 'right',
    marginBottom: 12,
    paddingHorizontal: 10,
  },
  sectionCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(6, 142, 119, 0.05)',
  },
  settingLeftContent: {},
  settingRightContent: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(6, 142, 119, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingTextContainer: {
    marginRight: 16,
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    color: '#333',
    textAlign: 'right',
  },
  settingSubtitle: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#666',
    textAlign: 'right',
    marginTop: 2,
  },
  logoutButton: {
    marginHorizontal: 4,
    backgroundColor: '#F35A3020',
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  logoutButtonText: {
    color: '#F35A30',
    fontSize: 16,
    fontFamily: 'Rubik-Bold',
  },
  footer: {
    alignItems: 'center',
    marginTop: 40,
    paddingBottom: 20,
  },
  versionText: {
    fontSize: 14,
    fontFamily: 'Rubik-Medium',
    color: '#999',
  },
  copyrightText: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#aaa',
    marginTop: 4,
    textAlign: 'center',
  },
});