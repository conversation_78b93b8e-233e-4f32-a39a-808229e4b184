// components/FieldCard.tsx

import { Ionicons } from '@expo/vector-icons';
import { Image } from 'expo-image';
import React from 'react';
import {
    Dimensions,
    Linking,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

interface Field {
  id: string;
  name: string;
  description: string;
  phone_number: string;
  location: string;
  lat: number;
  long: number;
  image: any;
}

interface FieldCardProps {
  field: Field;
}

const { width } = Dimensions.get('window');

const FieldCard: React.FC<FieldCardProps> = ({ field }) => {
  const handleCall = () => {
    Linking.openURL(`tel:${field.phone_number}`);
  };

  const handleLocation = () => {
    const url = `https://maps.google.com/?q=${field.lat},${field.long}`;
    Linking.openURL(url);
  };

  return (
    <View style={styles.cardContainer}>
      <Image source={field.image} style={styles.image} contentFit="cover" />
      <View style={styles.infoContainer}>
        <Text style={styles.title}>{field.name}</Text>
        <Text style={styles.description} numberOfLines={2}>
          {field.description}
        </Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity style={styles.button} onPress={handleCall}>
            <Ionicons name="call-outline" size={18} color="#068E77" />
            <Text style={styles.buttonText}>اتصال</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.button} onPress={handleLocation}>
            <Ionicons name="location-outline" size={18} color="#068E77" />
            <Text style={styles.buttonText}>الموقع</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    flexDirection: 'row-reverse',
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 12,
    marginBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 12,
  },
  infoContainer: {
    flex: 1,
    paddingRight: 12,
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    fontFamily: 'Rubik-Bold',
    color: '#333',
    textAlign: 'right',
  },
  description: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#777',
    textAlign: 'right',
    marginTop: 4,
  },
  buttonRow: {
    flexDirection: 'row-reverse',
    marginTop: 10,
    gap: 10,
  },
  button: {
    flexDirection: 'row-reverse',
    alignItems: 'center',
    backgroundColor: '#068E771A',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  buttonText: {
    marginLeft: 6,
    color: '#068E77',
    fontFamily: 'Rubik-Medium',
    fontSize: 12,
  },
});

export default FieldCard;