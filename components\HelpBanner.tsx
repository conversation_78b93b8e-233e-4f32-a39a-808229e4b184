import { Image } from 'expo-image';
import React from 'react';
import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

const { width } = Dimensions.get('window');

interface HelpBannerProps {
  onSubmitPress?: () => void;
}

const HelpBanner: React.FC<HelpBannerProps> = ({ onSubmitPress }) => {
  const handleSubmitPress = () => {
    if (onSubmitPress) {
      onSubmitPress();
    } else {
      console.log('Navigate to form page');
    }
  };

  return (
    <View style={styles.outerContainer}>
        <View style={styles.contentContainer}>
          <View style={styles.textSection}>
            <Text style={styles.title}>هل تحتاج إلى مساعدة؟</Text>
            <Text style={styles.description}>
              نحن هنا لمساعدتك , يمكنك تقديم شكوى أو مقترح لأى مشكلة تواجهك داخل
              مدينة الشيخ زايد
            </Text>
          </View>
          <View style={styles.container2}>
            <Text style={styles.description1}>
              نحن هنا لمساعدتك , يمكنك تقديم شكوى أو مقترح لأى مشكلة تواجهك داخل
              مدينة الشيخ زايد
            </Text>

            {/* --- FIX START: Wrap Image and Button in a new View --- */}
            <View
              style={{
                flexDirection: 'row', // This makes them side-by-side
                alignItems: 'center',   // This vertically aligns them
                width: '100%',
                marginTop: 15,
                marginBottom: 15,
                // Adds some space from the text above
              }}>
              
              {/* Image is placed FIRST to be on the LEFT */}
              <Image
                source={require('@/assets/images/helpban.png')}
                style={styles.image}
                contentFit="contain"
              />
              
              {/* Button is placed SECOND to be on the RIGHT */}
              <TouchableOpacity
                style={styles.button}
                onPress={handleSubmitPress}
                activeOpacity={0.8}>
                <Text style={styles.buttonText}>تقديم طلب</Text>
              </TouchableOpacity>
            </View>
            {/* --- FIX END --- */}
          </View>
        </View>
    </View>
  );
};

const styles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    width: '100%',
    paddingVertical: '2%',
  },
  container2: {
    flex: 1,
    width: '100%',
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    alignItems: 'center',
    padding: 10,
    
  },
  image: {
    // --- MODIFIED LINE ---
    width: '40%', // Adjusted width for side-by-side layout
    height: width * 0.25, // Adjusted height to be more proportional
    marginRight: 10, // Added margin to separate from button
  },
  contentContainer: {
    width: '100%',
    paddingHorizontal: 10,
    paddingVertical: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textSection: {
    flex: 1,
    fontWeight: '700',
    fontFamily: 'Rubik-Bold',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  title: {
    fontFamily: 'Rubik-Bold',
    fontSize: width * 0.045,
    textAlign: 'right',
    color: '#000000',
    writingDirection: 'rtl',
  },
  description: {
    fontFamily: 'Rubik-Medium',
    fontSize: 14,
    paddingBottom: 10,
    paddingTop: 10,
    paddingHorizontal: 10,
    textAlign: 'center',
    color: '#068E77',
    writingDirection: 'rtl',
    
  },
  description1: {
    fontFamily: 'Rubik-Medium',
    fontSize: 12,
    textAlign: 'center',
    paddingBottom: 10,
    paddingTop: 10,
    paddingLeft: 100,
    color: '#000000',
    writingDirection: 'rtl',
    
  },
  button: {
    // --- MODIFIED LINE ---
    width: '60%', // Adjusted width for side-by-side layout
    backgroundColor: '#F35A30',
    borderRadius: 29,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
  },
  buttonText: {
    color: '#FFFFFF',
    fontFamily: 'Rubik-Bold',
    fontWeight: 'bold',
    fontSize: width * 0.04,
    textAlign: 'center',
  },
});

export default React.memo(HelpBanner);