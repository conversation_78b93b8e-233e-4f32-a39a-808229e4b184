import { Ionicons } from '@expo/vector-icons';
import type { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { BlurView } from 'expo-blur';
import * as Haptics from 'expo-haptics';
import React from 'react';
import { Platform, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface Tab {
  name: string;
  label: string;
  icon: keyof typeof Ionicons.glyphMap;
  activeIcon: keyof typeof Ionicons.glyphMap;
}

// Define your routes. The keys can be anything, but values should match screen names.
const TABS: Tab[] = [
  { name: 'index', label: 'الرئيسية', icon: 'home-outline', activeIcon: 'home' },
  { name: 'news', label: 'الأخبار', icon: 'newspaper-outline', activeIcon: 'newspaper' },
  { name: 'services', label: 'الخدمات', icon: 'grid-outline', activeIcon: 'grid' },
  { name: 'events', label: 'الفعاليات', icon: 'calendar-outline', activeIcon: 'calendar' },
];

// The component now receives BottomTabBarProps
export default function ModernTabBar({ state, navigation }: BottomTabBarProps) {
  // ✅ DERIVE the active index from the navigation state. This is the source of truth.
  const activeIndex = state.index;
  const insets = useSafeAreaInsets();
  const indicatorPosition = useSharedValue(0);

  React.useEffect(() => {
    indicatorPosition.value = withSpring(activeIndex * (400 / TABS.length), {
      damping: 20,
      stiffness: 300,
    });
  }, [activeIndex]);

  const indicatorStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: indicatorPosition.value }],
  }));

  const handleTabPress = (routeName: string, isFocused: boolean) => {
    // Haptic feedback
    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    // Navigate to the tab. prevent re-navigating to the same tab.
    if (!isFocused) {
      navigation.navigate(routeName);
    }
  };

  return (
    <BlurView
      intensity={9.2}
      tint="systemUltraThinMaterialLight"
      style={[styles.container, { paddingBottom: insets.bottom }]}
      experimentalBlurMethod='dimezisBlurView'
      blurReductionFactor={5}
    >
      <View style={styles.colorOverlay} />
      <View style={styles.tabBar}>
        {state.routes.map((route, index) => {
          const isFocused = activeIndex === index;
          const tab = TABS.find(t => t.name === route.name);

          if (!tab) return null;

          return (
            <TabItem
              key={route.key}
              tab={tab}
              isActive={isFocused}
              onPress={() => handleTabPress(route.name, isFocused)}
            />
          );
        })}
      </View>
    </BlurView>
  );
}

function TabItem({ tab, isActive, onPress }: { tab: Tab; isActive: boolean; onPress: () => void }) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(isActive ? 1 : 0.7);

  React.useEffect(() => {
    scale.value = withSpring(isActive ? 1.2 : 1, {
      damping: 25,
      stiffness: 400,
    });
    opacity.value = withSpring(isActive ? 1 : 0.7, {
      damping: 25,
      stiffness: 400,
    });
  }, [isActive]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  return (
    <TouchableOpacity style={styles.tab} onPress={onPress} activeOpacity={0.8}>
      <Animated.View style={[styles.tabContent, animatedStyle]}>
        <Ionicons
          name={isActive ? tab.activeIcon : tab.icon}
          size={24}
          color={isActive ? '#f7dc6f' : '#ffffff'}
        />
        <Text style={[styles.tabText, { color: isActive ? '#f7dc6f' : '#ffffff' }]}>
          {tab.label}
        </Text>
      </Animated.View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: -15,
    left: 0,
    right: 0,
    overflow: 'hidden',
    
  },
  colorOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#F35A30',
  },
  tabBar: {
    flexDirection: 'row',
    height: '100%',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingBottom: 2,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    
  },
  tabContent: {
    alignItems: 'center',
    gap: 4,
    paddingTop: 12,
  },
  tabText: {
    fontSize: 11,
    fontWeight: '600',
    fontFamily: 'Rubik-Medium',
  },
});