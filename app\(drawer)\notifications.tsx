// app/(drawer)/notifications.tsx

import { Ionicons } from '@expo/vector-icons';
import React from 'react';
import {
    Dimensions,
    FlatList,
    SafeAreaView,
    StyleSheet,
    Text,
    View
} from 'react-native';

const { width } = Dimensions.get('window');

// Placeholder Data for Notifications
const notifications = [
  {
    id: '1',
    icon: 'newspaper-outline',
    title: 'خبر جديد: افتتاح حديقة الشيخ زايد',
    description: 'تم افتتاح الحديقة المركزية بحلتها الجديدة، مع إضافة مناطق ألعاب ومساحات خضراء.',
    time: 'منذ 5 دقائق',
    color: '#4A90E2',
  },
  {
    id: '2',
    icon: 'calendar-outline',
    title: 'تذكير بفعالية: ماراثون زايد الخيري',
    description: 'لا تنس موعد الماراثون السنوي غداً الساعة 8 صباحاً.',
    time: 'منذ 1 ساعة',
    color: '#FF6B6B',
  },
  {
    id: '3',
    icon: 'megaphone-outline',
    title: 'إعلان هام: انقطاع المياه',
    description: 'سيتم قطع المياه عن الحي الثالث للصيانة يوم السبت المقبل.',
    time: 'منذ 3 ساعات',
    color: '#F35A30',
  },
  {
    id: '4',
    icon: 'build-outline',
    title: 'مشروع جديد: تطوير الطرق',
    description: 'بدأت أعمال توسعة وتطوير المحاور الرئيسية في المدينة.',
    time: 'منذ 1 يوم',
    color: '#4ECDC4',
  },
  {
    id: '5',
    icon: 'wallet-outline',
    title: 'خدمة جديدة: الدفع الإلكتروني للفواتير',
    description: 'يمكنك الآن دفع فواتير المياه والكهرباء عبر التطبيق.',
    time: 'منذ 2 أيام',
    color: '#96CEB4',
  },
];

const NotificationItem = ({ item }) => (
  <View style={styles.notificationCard}>
    <View style={[styles.iconContainer, { backgroundColor: `${item.color}20` }]}>
      <Ionicons name={item.icon as any} size={28} color={item.color} />
    </View>
    <View style={styles.textContainer}>
      <Text style={styles.notificationTitle}>{item.title}</Text>
      <Text style={styles.notificationDescription}>{item.description}</Text>
      <Text style={styles.notificationTime}>{item.time}</Text>
    </View>
  </View>
);

export default function NotificationsScreen() {
  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.header}>
        <Text style={styles.title}>الإشعارات</Text>
      </View>
      <FlatList
        data={notifications}
        renderItem={({ item }) => <NotificationItem item={item} />}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={<Text style={styles.noResultsText}>لا توجد إشعارات جديدة.</Text>}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#EBDECE',
    paddingTop: 120, // Adjust for global header
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: width * 0.06,
    fontFamily: 'Rubik-Bold',
    color: '#068E77',
  },
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  notificationCard: {
    flexDirection: 'row-reverse',
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    alignItems: 'center',
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
    marginRight: 16,
  },
  notificationTitle: {
    fontSize: 16,
    fontFamily: 'Rubik-Bold',
    color: '#333',
    textAlign: 'right',
  },
  notificationDescription: {
    fontSize: 14,
    fontFamily: 'Rubik-Regular',
    color: '#666',
    textAlign: 'right',
    marginTop: 4,
  },
  notificationTime: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#999',
    textAlign: 'left', // Time is usually on the left in RTL context
    marginTop: 8,
  },
  noResultsText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#888',
    fontFamily: 'Rubik-Medium',
    marginTop: 40,
  },
});