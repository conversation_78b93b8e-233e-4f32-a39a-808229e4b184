// app/(drawer)/news/[id].tsx

import { Image } from 'expo-image';
import { useLocalSearchParams, useRouter } from 'expo-router';
import React from 'react';
import {
    Dimensions,
    SafeAreaView,
    ScrollView,
    StyleSheet,
    Text,
    View
} from 'react-native';

const { width } = Dimensions.get('window');

// --- Placeholder Data ---
// In a real app, you would fetch this by ID from your API
const placeholderNews = [
    {
        id: '1',
        title: 'افتتاح حديقة الشيخ زايد المركزية بعد التجديدات',
        date: '2025-07-10T10:00:00.000Z',
        category: 'أخبار المدينة',
        image: require('@/assets/images/event1.jpg'),
        content: `
شهدت مدينة الشيخ زايد اليوم افتتاح الحديقة المركزية بحلتها الجديدة بعد شهور من أعمال التطوير والتجديد. 
أضافت التجديدات مساحات خضراء واسعة، ومناطق ألعاب حديثة للأطفال، ومسارات مخصصة للمشي والجري، بالإضافة إلى نافورة راقصة في قلب الحديقة. 
وأكد رئيس جهاز المدينة أن هذا المشروع يأتي في إطار خطة شاملة لتطوير المرافق العامة وتحسين جودة الحياة للسكان، مشيراً إلى أن الحديقة ستكون متنفساً طبيعياً وصحياً لجميع أفراد العائلة.
        `,
    },
    // Add other news articles here...
];

export default function NewsDetailScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();

  // Find the news article by its ID.
  const article = placeholderNews.find(item => item.id === id);

  if (!article) {
    return (
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.container}>
          <Text style={styles.errorText}>عفواً، هذا الخبر غير موجود.</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <ScrollView>
        <Image source={article.image} style={styles.image} />
        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={styles.category}>{article.category}</Text>
            <Text style={styles.date}>{new Date(article.date).toLocaleDateString('ar-EG', { day: 'numeric', month: 'long', year: 'numeric' })}</Text>
          </View>
          <Text style={styles.title}>{article.title}</Text>
          <View style={styles.separator} />
          <Text style={styles.content}>{article.content.trim()}</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#EBDECE',
  },
  image: {
    width: '100%',
    height: width * 0.7, // Make image taller for detail view
  },
  contentContainer: {
    padding: 20,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginTop: -20, // Pull the content slightly over the image
  },
  header: {
    flexDirection: 'row-reverse',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  category: {
    fontFamily: 'Rubik-Medium',
    fontSize: 14,
    color: '#F35A30',
    backgroundColor: '#F35A301A',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    overflow: 'hidden',
  },
  date: {
    fontFamily: 'Rubik-Regular',
    fontSize: 14,
    color: '#666',
  },
  title: {
    fontFamily: 'Rubik-Bold',
    fontSize: 24,
    color: '#333',
    textAlign: 'right',
    lineHeight: 34,
  },
  separator: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 16,
  },
  content: {
    fontFamily: 'Rubik-Regular',
    fontSize: 17,
    color: '#444',
    textAlign: 'right',
    lineHeight: 28,
  },
  errorText: {
    fontFamily: 'Rubik-Bold',
    fontSize: 18,
    color: '#888',
    textAlign: 'center',
    marginTop: 50,
  },
});