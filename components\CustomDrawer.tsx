// components/CustomDrawer.tsx

import { Ionicons } from '@expo/vector-icons';
import { DrawerContentScrollView } from '@react-navigation/drawer';
import { BlurView } from 'expo-blur';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import {
  SafeAreaView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';

type IconName = keyof typeof Ionicons.glyphMap;

interface MenuItem {
  name: string;
  icon: IconName;
  route: string;
}

const userData = {
  name: 'زائر',
  profileImage: require('@/assets/images/icon.png'),
};

export function CustomDrawerContent(props: any) {
  const menuItems: MenuItem[] = [
    { name: 'الصفحة الرئيسية', icon: 'home-outline' as IconName, route: '/(drawer)/(tabs)/' },
    { name: 'الأخبار', icon: 'newspaper-outline' as IconName, route: '/(drawer)/(tabs)/news' },
    { name: 'الخدمات', icon: 'grid-outline' as IconName, route: '/(drawer)/(tabs)/services' },
    { name: 'الفعاليات', icon: 'calendar-outline' as IconName, route: '/(drawer)/(tabs)/events' },
    { name: 'حول المدينة', icon: 'information-circle-outline' as IconName, route: '/(drawer)/about' },
    { name: 'اتصل بنا', icon: 'call-outline' as IconName, route: '/(drawer)/contact' },
    { name: 'الإعدادات', icon: 'settings-outline' as IconName, route: '/(drawer)/settings' },
  ];

  const handleProfilePress = () => {
    props.navigation.closeDrawer();
    router.push('/(drawer)/settings');
  };

  return (
    <BlurView
      intensity={15}
      tint="light"
      style={styles.container}
      experimentalBlurMethod="dimezisBlurView"
    >
      <SafeAreaView style={styles.safeArea}>
        <View style={styles.colorOverlay} />

        <TouchableOpacity
          style={styles.drawerHeader}
          onPress={handleProfilePress}
          activeOpacity={0.8}
        >
          <Image
            source={userData.profileImage}
            style={styles.profileImage}
            contentFit="cover"
          />
          <Text style={styles.profileName}>{userData.name}</Text>
        </TouchableOpacity>

        <DrawerContentScrollView
          {...props}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {menuItems.map((item) => (
            <TouchableOpacity
              key={item.route}
              style={styles.menuItem}
              onPress={() => {
                props.navigation.closeDrawer();
                router.push(item.route as any);
              }}
              activeOpacity={0.7}
            >
              {/* --- The layout is now updated for RTL --- */}
              <View style={styles.menuItemContent}>
                <Text style={styles.menuText}>{item.name}</Text>
                <Ionicons
                  name={item.icon}
                  size={24}
                  color="#068E77"
                  style={styles.menuIcon}
                />
              </View>
            </TouchableOpacity>
          ))}
        </DrawerContentScrollView>

        <View style={styles.footer}>
          <Text style={styles.version}>الإصدار 1.0.0</Text>
        </View>
      </SafeAreaView>
    </BlurView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  colorOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  drawerHeader: {
    padding: 20,
    paddingTop: 40,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(6, 142, 119, 0.2)',
  },
  profileImage: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: '#068E77',
  },
  profileName: {
    fontSize: 18,
    fontFamily: 'Rubik-Bold',
    color: '#068E77',
  },
  scrollContent: {
    paddingTop: 20,
  },
  menuItem: {
    marginHorizontal: 15,
    marginVertical: 3,
    borderRadius: 12,
    overflow: 'hidden',
  },
  // --- Updated Styles for Menu Items ---
  menuItemContent: {
    flexDirection: 'row', // Changed from 'row-reverse'
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
  },
  menuIcon: {
    // Icon is now the first item on the left
  },
  menuText: {
    fontSize: 16,
    fontFamily: 'Rubik-Medium',
    color: '#333',
    textAlign: 'right',
    flex: 1,
    // Add margin to create space between text and icon
    marginRight: 20, 
  },
  // --- End Updated Styles ---
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: 'rgba(6, 142, 119, 0.2)',
  },
  version: {
    fontSize: 12,
    fontFamily: 'Rubik-Regular',
    color: '#666',
  },
});